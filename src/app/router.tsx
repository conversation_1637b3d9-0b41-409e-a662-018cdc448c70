import { useEffect, useState } from 'react';
import ProtectedRoute from '../components/ui/ProtectedRoute';
import DashboardPage from '../pages/DashboardPage';
import DeviceMonitoringPage from '../pages/DeviceMonitoringPage';
import AlertMonitoringPage from '../pages/AlertMonitoringPage';
import ReportPage from '../pages/ReportPage';
import UserManagementPage from '../pages/UserManagementPage';
import SettingPage from '../pages/SettingPage';
import AboutPage from '../pages/AboutPage';

export function AppRouter() {
  const [route, setRoute] = useState<string>(window.location.hash || '#/dashboard');

  useEffect(() => {
    const onHashChange = () => setRoute(window.location.hash || '#/dashboard');
    window.addEventListener('hashchange', onHashChange);
    if (!window.location.hash) {
      window.location.hash = '#/dashboard';
    }
    return () => window.removeEventListener('hashchange', onHashChange);
  }, []);

  const renderPage = () => {
    switch (route) {
      case '#/dashboard':
        return <DashboardPage />;
      case '#/monitoring':
        return <DeviceMonitoringPage />;
      case '#/alerts':
        return <AlertMonitoringPage />;
      case '#/reports':
        return <ReportPage />;
      case '#/users':
        return <UserManagementPage />;
      case '#/settings':
        return <SettingPage />;
      case '#/about':
        return <AboutPage />;
      default:
        return <DashboardPage />;
    }
  };

  return (
    <ProtectedRoute>
      {renderPage()}
    </ProtectedRoute>
  );
}
