import React from 'react';
import { ThemeProvider } from '@/shared/lib/ThemeContext';
import { AuthProvider } from '@/domains/auth/context/AuthContext';
import { FiltersProvider } from '@/shared/lib/FiltersContext';

interface AppProvidersProps {
  children: React.ReactNode;
}

export function AppProviders({ children }: AppProvidersProps) {
  return (
    <ThemeProvider>
      <AuthProvider>
        <FiltersProvider>
          {children}
        </FiltersProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
