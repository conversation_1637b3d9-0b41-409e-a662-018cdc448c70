export type DeviceType = 'lighthouse' | 'beacon' | 'marker';
export type DeviceStatus = 'operational' | 'maintenance' | 'offline';
export type DeviceProvider = 'sealite' | 'vjlight';

export interface NavaidDevice {
  id: string;
  name: string;
  nameVi: string;
  type: DeviceType;
  status: DeviceStatus;
  location: string;
  locationVi: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  port: string;
  serialNumber: string;
  batteryVoltage: number;
  solarCharge: number;
  loadAmps: number;
  flashCode: string;
  lightStatus: 'ON' | 'OFF';
  lastUpdateTime: string;
  positionStatus: 'OK' | 'OFF';
  ialaName: string;
  signalStrength: number;
  lastMaintenance: string;
  nextMaintenance: string;
  uptime: number;
  provider: DeviceProvider;
}

export interface AISVessel {
  id: string;
  name: string;
  mmsi: string;
  type: 'cargo' | 'tanker' | 'passenger' | 'fishing' | 'tug' | 'pilot' | 'military' | 'pleasure';
  coordinates: {
    lat: number;
    lng: number;
  };
  heading: number; // degrees
  speed: number; // knots
  course: number; // degrees
  status: 'underway' | 'anchored' | 'moored' | 'aground' | 'not_under_command';
  length: number; // meters
  beam: number; // meters
  draft: number; // meters
  destination: string;
  eta: string;
  lastUpdate: string;
}

export const mockNavaidDevices: NavaidDevice[] = [
  {
    id: "01",
    name: "WT11-S1",
    nameVi: "WT11-S1",
    type: "lighthouse",
    status: "maintenance",
    location: "Tan Thuan",
    locationVi: "Tân Thuận",
    coordinates: { lat: 8.985541, lng: 105.425316 },
    port: "Tân Thuận",
    serialNumber: "**********",
    batteryVoltage: 3.9,
    solarCharge: 0,
    loadAmps: 0,
    flashCode: "009",
    lightStatus: "ON",
    lastUpdateTime: "05/07/2025 18:06:09",
    positionStatus: "OK",
    ialaName: "Light House Starboard Lateral",
    signalStrength: 85,
    lastMaintenance: "2025-04-12",
    nextMaintenance: "2025-07-12",
    uptime: 97.8,
    provider: "sealite"
  },
  {
    id: "02",
    name: "WT24-S1",
    nameVi: "WT24-S1",
    type: "lighthouse",
    status: "operational",
    location: "Tan Thuan",
    locationVi: "Tân Thuận",
    coordinates: { lat: 8.956722, lng: 105.425906 },
    port: "Tân Thuận",
    serialNumber: "**********",
    batteryVoltage: 4.0,
    solarCharge: 0,
    loadAmps: 0,
    flashCode: "009",
    lightStatus: "ON",
    lastUpdateTime: "05/07/2025 18:05:53",
    positionStatus: "OK",
    ialaName: "Light House Starboard Lateral",
    signalStrength: 98,
    lastMaintenance: "2025-04-10",
    nextMaintenance: "2025-07-10",
    uptime: 99.2,
    provider: "sealite"
  },
  {
    id: "03",
    name: "WT01-S1",
    nameVi: "WT01-S1",
    type: "lighthouse",
    status: "operational",
    location: "Tan Thuan",
    locationVi: "Tân Thuận",
    coordinates: { lat: 9.00402, lng: 105.424551 },
    port: "Tân Thuận",
    serialNumber: "**********",
    batteryVoltage: 4.0,
    solarCharge: 0.01,
    loadAmps: 0,
    flashCode: "009",
    lightStatus: "ON",
    lastUpdateTime: "05/07/2025 18:07:22",
    positionStatus: "OK",
    ialaName: "Light House Starboard Lateral",
    signalStrength: 96,
    lastMaintenance: "2025-04-08",
    nextMaintenance: "2025-07-08",
    uptime: 99.5,
    provider: "sealite"
  },
  {
    id: "04",
    name: "DGCMTH3",
    nameVi: "DGCMTH3",
    type: "marker",
    status: "operational",
    location: "Tan Thuan",
    locationVi: "Tân Thuận",
    coordinates: { lat: 9.008, lng: 105.425213 },
    port: "Tân Thuận",
    serialNumber: "**********",
    batteryVoltage: 4.17,
    solarCharge: 0,
    loadAmps: 0,
    flashCode: "075",
    lightStatus: "ON",
    lastUpdateTime: "05/07/2025 18:00:33",
    positionStatus: "OFF",
    ialaName: "Light House Starboard Lateral",
    signalStrength: 92,
    lastMaintenance: "2025-04-15",
    nextMaintenance: "2025-07-15",
    uptime: 98.8,
    provider: "vjlight"
  },
  {
    id: "05",
    name: "DGCMTH1",
    nameVi: "DGCMTH1",
    type: "marker",
    status: "operational",
    location: "Tan Thuan",
    locationVi: "Tân Thuận",
    coordinates: { lat: 8.956677, lng: 105.425925 },
    port: "Tân Thuận",
    serialNumber: "**********",
    batteryVoltage: 4.17,
    solarCharge: 0,
    loadAmps: 0,
    flashCode: "075",
    lightStatus: "ON",
    lastUpdateTime: "05/07/2025 18:01:25",
    positionStatus: "OFF",
    ialaName: "Light House Starboard Lateral",
    signalStrength: 89,
    lastMaintenance: "2025-04-20",
    nextMaintenance: "2025-07-20",
    uptime: 97.5,
    provider: "vjlight"
  },
  {
    id: "06",
    name: "DGCMTH2",
    nameVi: "DGCMTH2",
    type: "marker",
    status: "operational",
    location: "Tan Thuan",
    locationVi: "Tân Thuận",
    coordinates: { lat: 8.957397, lng: 105.425946 },
    port: "Tân Thuận",
    serialNumber: "**********",
    batteryVoltage: 4.18,
    solarCharge: 0,
    loadAmps: 0,
    flashCode: "075",
    lightStatus: "ON",
    lastUpdateTime: "05/07/2025 18:00:28",
    positionStatus: "OFF",
    ialaName: "Light House Starboard Lateral",
    signalStrength: 91,
    lastMaintenance: "2025-04-18",
    nextMaintenance: "2025-07-18",
    uptime: 98.1,
    provider: "vjlight"
  },
  {
    id: "07",
    name: "WT12-S2",
    nameVi: "WT12-S2",
    type: "lighthouse",
    status: "operational",
    location: "Tan Thuan",
    locationVi: "Tân Thuận",
    coordinates: { lat: 8.958140, lng: 105.425621 },
    port: "Tân Thuận",
    serialNumber: "**********",
    batteryVoltage: 4.0,
    solarCharge: 0,
    loadAmps: 0,
    flashCode: "009",
    lightStatus: "ON",
    lastUpdateTime: "05/07/2025 18:05:10",
    positionStatus: "OK",
    ialaName: "Light House Starboard Lateral",
    signalStrength: 94,
    lastMaintenance: "2025-04-12",
    nextMaintenance: "2025-07-12",
    uptime: 99.1,
    provider: "sealite"
  }
];

export const mockAISVessels: AISVessel[] = [
  {
    id: "V001",
    name: "SAIGON EXPRESS",
    mmsi: "574123456",
    type: "cargo",
    coordinates: { lat: 9.015, lng: 105.430 },
    heading: 45,
    speed: 12.5,
    course: 47,
    status: "underway",
    length: 180,
    beam: 28,
    draft: 8.5,
    destination: "SINGAPORE",
    eta: "05/07/2025 22:00",
    lastUpdate: "05/07/2025 18:15:30"
  },
  {
    id: "V002",
    name: "MEKONG STAR",
    mmsi: "574234567",
    type: "tanker",
    coordinates: { lat: 8.995, lng: 105.415 },
    heading: 180,
    speed: 8.2,
    course: 185,
    status: "underway",
    length: 220,
    beam: 32,
    draft: 12.0,
    destination: "HO CHI MINH",
    eta: "05/07/2025 20:30",
    lastUpdate: "05/07/2025 18:12:45"
  },
  {
    id: "V003",
    name: "VUNG TAU PILOT",
    mmsi: "574345678",
    type: "pilot",
    coordinates: { lat: 9.005, lng: 105.420 },
    heading: 90,
    speed: 15.0,
    course: 92,
    status: "underway",
    length: 25,
    beam: 6,
    draft: 2.5,
    destination: "PILOT STATION",
    eta: "05/07/2025 19:00",
    lastUpdate: "05/07/2025 18:18:12"
  },
  {
    id: "V004",
    name: "DONG NAI FISHER",
    mmsi: "574456789",
    type: "fishing",
    coordinates: { lat: 8.980, lng: 105.440 },
    heading: 270,
    speed: 6.5,
    course: 275,
    status: "underway",
    length: 35,
    beam: 8,
    draft: 3.0,
    destination: "FISHING GROUND",
    eta: "05/07/2025 21:00",
    lastUpdate: "05/07/2025 18:10:20"
  },
  {
    id: "V005",
    name: "PACIFIC VOYAGER",
    mmsi: "574567890",
    type: "passenger",
    coordinates: { lat: 9.020, lng: 105.435 },
    heading: 315,
    speed: 18.0,
    course: 318,
    status: "underway",
    length: 150,
    beam: 22,
    draft: 6.5,
    destination: "PHAN THIET",
    eta: "06/07/2025 08:00",
    lastUpdate: "05/07/2025 18:16:55"
  },
  {
    id: "V006",
    name: "TUG BOAT 01",
    mmsi: "574678901",
    type: "tug",
    coordinates: { lat: 8.990, lng: 105.425 },
    heading: 0,
    speed: 0,
    course: 0,
    status: "anchored",
    length: 30,
    beam: 10,
    draft: 4.0,
    destination: "STANDBY",
    eta: "N/A",
    lastUpdate: "05/07/2025 18:05:30"
  },
  {
    id: "V007",
    name: "CONTAINER KING",
    mmsi: "574789012",
    type: "cargo",
    coordinates: { lat: 10.340, lng: 107.080 },
    heading: 225,
    speed: 14.8,
    course: 228,
    status: "underway",
    length: 200,
    beam: 30,
    draft: 10.0,
    destination: "HONG KONG",
    eta: "07/07/2025 14:00",
    lastUpdate: "05/07/2025 18:20:15"
  },
  {
    id: "V008",
    name: "NAVY PATROL 03",
    mmsi: "574890123",
    type: "military",
    coordinates: { lat: 16.050, lng: 108.200 },
    heading: 120,
    speed: 20.0,
    course: 125,
    status: "underway",
    length: 80,
    beam: 12,
    draft: 4.5,
    destination: "PATROL AREA",
    eta: "N/A",
    lastUpdate: "05/07/2025 18:14:40"
  },
  {
    id: "V009",
    name: "LEISURE YACHT",
    mmsi: "574901234",
    type: "pleasure",
    coordinates: { lat: 21.025, lng: 105.850 },
    heading: 180,
    speed: 0,
    course: 0,
    status: "moored",
    length: 15,
    beam: 4,
    draft: 1.5,
    destination: "MARINA",
    eta: "N/A",
    lastUpdate: "05/07/2025 18:08:25"
  },
  {
    id: "V010",
    name: "BULK CARRIER 88",
    mmsi: "575012345",
    type: "cargo",
    coordinates: { lat: 9.000, lng: 105.410 },
    heading: 60,
    speed: 10.5,
    course: 65,
    status: "underway",
    length: 190,
    beam: 26,
    draft: 9.2,
    destination: "JAPAN",
    eta: "08/07/2025 06:00",
    lastUpdate: "05/07/2025 18:17:30"
  }
];

export interface ServiceRequest {
  id: string;
  navaidId: string;
  navaidName: string;
  status: 'open' | 'closed' | 'in-progress';
  subject: string;
  category: string;
  requester: string;
  phone: string;
  requestTime: string;
  description: string;
  comments: string;
}

export const mockServiceRequests: ServiceRequest[] = [
  {
    id: "REQ001",
    navaidId: "01",
    navaidName: "WT11-S1",
    status: "open",
    subject: "Breakdown_Not Charging",
    category: "SEALITE",
    requester: "Long Tran",
    phone: "0123456789",
    requestTime: "24/06/2025 18:23:07",
    description: "Maintenance Request: Breakdown Request: Not Charging",
    comments: "cần kiểm tra, testing"
  },
  {
    id: "REQ002",
    navaidId: "05",
    navaidName: "DGCMTH1",
    status: "closed",
    subject: "Maintenance_4 Month Day Marker Service",
    category: "LIGHTHOUSE",
    requester: "Duc Tran",
    phone: "0987654321",
    requestTime: "09/05/2025 12:30:39",
    description: "Routine 4-month maintenance service for day marker",
    comments: "Hoàn thành bảo trì định kỳ"
  }
];