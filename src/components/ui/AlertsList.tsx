import React from 'react';
import { Alert<PERSON>riangle, Info, AlertCircle, Wrench, Battery, Signal } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { mockServiceRequests } from '@/data/mockData';

const AlertsList: React.FC = () => {
  const { t } = useTranslation();

  const alerts = [
    {
      id: 2,
      type: 'warning',
      device: 'WT11-S1',
      location: 'Tân Thuận',
      message: '<PERSON><PERSON><PERSON><PERSON> áp pin thấp - Cần kiểm tra hệ thống sạc',
      time: '05/07/2025 18:06:09',
      isAllDay: false,
      category: 'SEALITE'
    },
    {
      id: 3,
      type: 'warning',
      device: 'DGCMTH1',
      location: 'Tân Thuận',
      message: 'Đang trong quá trình bảo trì định kỳ',
      time: '05/07/2025 17:45:30',
      isAllDay: false,
      category: 'VJLIGHT'
    },
    {
      id: 4,
      type: 'info',
      device: 'DGCMTH1',
      location: 'Tân Thuận',
      message: '<PERSON><PERSON><PERSON> tr<PERSON> định kỳ sắp tới trong 30 ngày',
      time: '05/07/2025',
      isAllDay: true,
      category: 'VJLIGHT'
    },
    {
      id: 5,
      type: 'info',
      device: 'WT01 S1',
      location: 'Tân Thuận',
      message: 'Thiết bị hoạt động bình thường (S/N: 0813392941)',
      time: '05/07/2025 10:00:00',
      isAllDay: false,
      category: 'SEALITE'
    },
    {
      id: 6,
      type: 'critical',
      device: 'DGCMTH1',
      location: 'Tân Thuận',
      message: 'Thiết bị ngoại tuyến - Không phát hiện tín hiệu GPS (S/N: 0338605890)',
      time: '05/07/2025 10:00:00',
      isAllDay: false,
      category: 'VJLIGHT'
    },
    {
      id: 7,
      type: 'critical',
      device: 'DGCMTH2',
      location: 'Tân Thuận',
      message: 'Thiết bị ngoại tuyến - Không phát hiện tín hiệu GPS (S/N: 0337912595)',
      time: '05/07/2025 10:00:00',
      isAllDay: false,
      category: 'VJLIGHT'
    },
    {
      id: 8,
      type: 'critical',
      device: 'DGCMTH3',
      location: 'Tân Thuận',
      message: 'Thiết bị ngoại tuyến - Không phát hiện tín hiệu GPS (S/N: 0337915882)',
      time: '05/07/2025 10:00:00',
      isAllDay: false,
      category: 'VJLIGHT'
    },
    {
      id: 9,
      type: 'info',
      device: 'WT12-S2',
      location: 'Tân Thuận',
      message: 'Thiết bị hoạt động bình thường (S/N: 0813503941)',
      time: '05/07/2025 10:00:00',
      isAllDay: false,
      category: 'SEALITE'
    },
    {
      id: 10,
      type: 'info',
      device: 'WT11-S1',
      location: 'Tân Thuận',
      message: 'Thiết bị hoạt động bình thường (S/N: 0813395941)',
      time: '05/07/2025 10:00:00',
      isAllDay: false,
      category: 'SEALITE'
    },
    {
      id: 11,
      type: 'info',
      device: 'WT24-S1',
      location: 'Tân Thuận',
      message: 'Thiết bị hoạt động bình thường (S/N: 0813375941)',
      time: '05/07/2025 10:00:00',
      isAllDay: false,
      category: 'SEALITE'
    },
  ];

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">{t('alerts:active.alerts')}</h2>

      <div className="space-y-4">
        {alerts.map(alert => (
          <div
            key={alert.id}
            className={`p-4 rounded-lg border-l-4 ${alert.type === 'critical' ? 'border-red-500 bg-red-500 bg-opacity-10' :
                alert.type === 'warning' ? 'border-yellow-500 bg-yellow-500 bg-opacity-10' :
                  'border-blue-500 bg-blue-500 bg-opacity-10'
              }`}
          >
            <div className="flex items-start">
              <div className="mr-3 mt-1">
                {alert.type === 'critical' ? (
                  <AlertCircle size={18} className="text-red-500" />
                ) : alert.type === 'warning' ? (
                  <AlertTriangle size={18} className="text-yellow-500" />
                ) : (
                  <Info size={18} className="text-blue-500" />
                )}
              </div>
              <div className="flex-1">
                <div className="flex justify-between">
                  <h3 className="font-semibold">{alert.device}</h3>
                  <div className="flex items-center">
                    <span className={`text-xs px-2 py-1 rounded-full mr-2 ${alert.category === 'SEALITE' ? 'bg-blue-600' :
                        alert.category === 'LIGHTHOUSE' ? 'bg-purple-600' :
                          alert.category === 'BEACON' ? 'bg-green-600' :
                            alert.category === 'VJLIGHT' ? 'bg-orange-600' : 'bg-gray-600'
                      }`}>
                      {alert.category}
                    </span>
                    {alert.isAllDay && (
                      <span className="text-xs px-2 py-1 rounded-full bg-gray-700">
                        {t('common:all.day')}
                      </span>
                    )}
                  </div>
                </div>
                <p className="text-sm text-gray-300">{alert.message}</p>
                <div className="flex justify-between mt-2 text-xs text-gray-400">
                  <span>{alert.location}</span>
                  <span>{alert.time}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6">
        <h3 className="font-semibold mb-3">{t('dashboard:service.requests')}</h3>
        <div className="space-y-3">
          {mockServiceRequests.map(request => (
            <div key={request.id} className="flex justify-between items-center p-3 bg-gray-800 rounded-lg">
              <div>
                <div className="font-medium flex items-center">
                  <Wrench size={14} className="mr-2 text-orange-500" />
                  {request.navaidName} - {request.subject}
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {t('alerts:requester')}: {request.requester} | {t('alerts:category')}: {request.category}
                </div>
                <div className="text-xs text-gray-400">{request.requestTime}</div>
              </div>
              <div className={`text-xs px-2 py-1 rounded-full ${request.status === 'open' ? 'bg-red-500' :
                  request.status === 'closed' ? 'bg-green-500' : 'bg-yellow-500'
                }`}>
                {t(request.status)}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-6">
        <h3 className="font-semibold mb-3">{t('alerts:system.reports')}</h3>
        <div className="space-y-3">
          <div className="flex justify-between items-center p-3 bg-gray-800 rounded-lg">
            <div>
              <div className="font-medium flex items-center">
                <Battery size={14} className="mr-2 text-green-500" />
                Báo cáo điện áp pin hệ thống
              </div>
              <div className="text-xs text-gray-400">05/07/2025</div>
            </div>
            <div className="text-xs bg-green-500 px-2 py-1 rounded-full text-white">18:00</div>
          </div>

          <div className="flex justify-between items-center p-3 bg-gray-800 rounded-lg">
            <div>
              <div className="font-medium flex items-center">
                <Signal size={14} className="mr-2 text-blue-500" />
                Báo cáo tình trạng tín hiệu GPS
              </div>
              <div className="text-xs text-gray-400">05/07/2025</div>
            </div>
            <div className="text-xs bg-blue-500 px-2 py-1 rounded-full text-white">17:30</div>
          </div>

          <div className="flex justify-between items-center p-3 bg-gray-800 rounded-lg">
            <div>
              <div className="font-medium flex items-center">
                <AlertTriangle size={14} className="mr-2 text-yellow-500" />
                Báo cáo bảo trì định kỳ tháng 7
              </div>
              <div className="text-xs text-gray-400">01/07/2025</div>
            </div>
            <div className="text-xs bg-yellow-500 px-2 py-1 rounded-full text-white">09:00</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AlertsList;