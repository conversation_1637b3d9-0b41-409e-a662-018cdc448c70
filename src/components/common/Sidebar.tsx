import React, { useEffect, useState } from 'react';
import {
  BarChart3,
  AlertCircle,
  Settings,
  HelpCircle,
  Users,
  MapPin,
  LogOut,
  Monitor,
  ChevronLeft,
  ChevronRight,
  ClipboardEdit,
  AlertTriangleIcon
} from 'lucide-react';
import { useTheme } from '@/shared/lib/ThemeContext';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/domains/auth/context/AuthContext';
import ThemeToggle from './ThemeToggle';
import LanguageToggle from './LanguageToggle';

interface SidebarProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, onToggleCollapse }) => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const { user, logout } = useAuth();
  const appVersion = __APP_VERSION__;
  const [route, setRoute] = useState<string>(typeof window !== 'undefined' ? (window.location.hash || '#/dashboard') : '#/dashboard');

  useEffect(() => {
    const onHashChange = () => setRoute(window.location.hash || '#/dashboard');
    window.addEventListener('hashchange', onHashChange);
    return () => window.removeEventListener('hashchange', onHashChange);
  }, []);

  const sidebarWidthClass = isCollapsed ? 'w-20' : 'w-64';
  const baseBg = theme === 'dark' ? 'bg-gray-900' : 'bg-white';
  const borderColor = theme === 'dark' ? 'border-gray-700' : 'border-gray-200';

  return (
    <div className={`${sidebarWidthClass} ${baseBg} border-r ${borderColor} flex flex-col h-full relative`}> 
      <div className={`p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'}`}>
        <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'} flex-1`}>
          <div className="w-10 h-10 flex items-center justify-center">
            <img
              src="/assets/marine.png"
              alt={t('navigation:app.title')}
              className="w-full h-full object-contain"
            />
          </div>
          {!isCollapsed && (
            <div>
              <h1 className="font-bold text-lg">{t('navigation:app.title')}</h1>
              <p className="text-xs text-gray-400">{t('navigation:device.management')}</p>
              <div className="mt-2 inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-[10px] font-semibold bg-blue-500/10 text-blue-300 border border-blue-400/40 tracking-wide">
                <span>v{appVersion}-beta</span>
              </div>
            </div>
          )}
        </div>
        <button
          type="button"
          onClick={onToggleCollapse}
          className={`p-2 rounded-md transition ${theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {isCollapsed ? (
            <ChevronRight size={18} className="text-gray-400" />
          ) : (
            <ChevronLeft size={18} className="text-gray-400" />
          )}
        </button>
      </div>
      {/* User Info Section */}
      {user && (
        <div className={`p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'}`}>
          <div className="w-10 h-10 min-w-[2.5rem] rounded-full bg-orange-500 flex items-center justify-center text-white font-bold text-lg">
            {user.username.charAt(0).toUpperCase()}
          </div>
          {!isCollapsed && (
            <div>
              <p className="font-medium">{user.username}</p>
            </div>
          )}
          <button
            onClick={logout}
            className={`${isCollapsed ? 'ml-2' : 'ml-auto'} ${theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-100'} p-2 rounded-md transition-colors`}
            title="Đăng xuất"
          >
            <LogOut size={18} className="text-gray-400" />
          </button>
        </div>
      )}
      {/* Navigation */}
      <nav className="p-2 overflow-y-auto">
        <ul className="space-y-1">
          <li>
            <a
              href="#/dashboard"
              className={`relative flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'} ${isCollapsed ? 'px-2' : 'px-4'} py-3 rounded-md ${
                route.startsWith('#/dashboard')
                  ? theme === 'dark'
                    ? 'bg-gray-800'
                    : 'bg-gray-100'
                  : theme === 'dark'
                  ? 'hover:bg-gray-800'
                  : 'hover:bg-gray-100'
              }`}
              title={t('navigation:dashboard.nav')}
            >
              <MapPin size={20} className="text-gray-400" />
              {!isCollapsed && <span>{t('navigation:dashboard.nav')}</span>}
            </a>
          </li>
          <li>
            <a
              href="#/monitoring"
              className={`relative flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'} ${isCollapsed ? 'px-2' : 'px-4'} py-3 rounded-md ${
                route.startsWith('#/monitoring')
                  ? theme === 'dark'
                    ? 'bg-gray-800'
                    : 'bg-gray-100'
                  : theme === 'dark'
                  ? 'hover:bg-gray-800'
                  : 'hover:bg-gray-100'
              }`}
              title={t('device:device.monitoring')}
            >
              <Monitor size={20} className="text-gray-400" />
              {!isCollapsed && <span>{t('device:device.monitoring')}</span>}
              <span
                className={`bg-orange-500 text-xs rounded-full w-5 h-5 flex items-center justify-center ${
                  isCollapsed ? 'absolute -top-1 -right-1' : 'ml-auto'
                }`}
              >
                3
              </span>
            </a>
          </li>
          <li>
            <a
              href="#/alerts"
              className={`relative flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'} ${isCollapsed ? 'px-2' : 'px-4'} py-3 rounded-md ${
                route.startsWith('#/alerts')
                  ? theme === 'dark'
                    ? 'bg-gray-800'
                    : 'bg-gray-100'
                  : theme === 'dark'
                  ? 'hover:bg-gray-800'
                  : 'hover:bg-gray-100'
              }`}
              title={t('navigation:device.alerts')}
            >
              <AlertTriangleIcon size={20} className="text-gray-400" />
              {!isCollapsed && <span>{t('navigation:device.alerts')}</span>}
              <span
                className={`bg-orange-500 text-xs rounded-full w-5 h-5 flex items-center justify-center ${
                  isCollapsed ? 'absolute -top-1 -right-1' : 'ml-auto'
                }`}
              >
                5
              </span>
            </a>
          </li>
          <li>
            <a
              href="#/reports"
              className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'} ${isCollapsed ? 'px-2' : 'px-4'} py-3 rounded-md ${
                route.startsWith('#/reports')
                  ? theme === 'dark'
                    ? 'bg-gray-800'
                    : 'bg-gray-100'
                  : theme === 'dark'
                  ? 'hover:bg-gray-800'
                  : 'hover:bg-gray-100'
              }`}
              title={t('navigation:maintenance.cases')}
            >
              <BarChart3 size={20} className="text-gray-400" />
              {!isCollapsed && <span>{t('navigation:maintenance.cases')}</span>}
            </a>
          </li>
          <li>
            <a
              href="#/users"
              className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'} ${isCollapsed ? 'px-2' : 'px-4'} py-3 rounded-md ${
                route.startsWith('#/users')
                  ? theme === 'dark'
                    ? 'bg-gray-800'
                    : 'bg-gray-100'
                  : theme === 'dark'
                  ? 'hover:bg-gray-800'
                  : 'hover:bg-gray-100'
              }`}
              title={t('navigation:users.management')}
            >
              <Users size={20} className="text-gray-400" />
              {!isCollapsed && <span>{t('navigation:users.management')}</span>}
            </a>
          </li>
          <li>
            <a
              href="#/settings"
              className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'} ${isCollapsed ? 'px-2' : 'px-4'} py-3 rounded-md ${
                route.startsWith('#/settings')
                  ? theme === 'dark'
                    ? 'bg-gray-800'
                    : 'bg-gray-100'
                  : theme === 'dark'
                  ? 'hover:bg-gray-800'
                  : 'hover:bg-gray-100'
              }`}
              title={t('navigation:settings')}
            >
              <Settings size={20} className="text-gray-400" />
              {!isCollapsed && <span>{t('navigation:settings')}</span>}
            </a>
          </li>
          <li>
            <a
              href="#/about"
              className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3'} ${isCollapsed ? 'px-2' : 'px-4'} py-3 rounded-md ${
                route.startsWith('#/about')
                  ? theme === 'dark'
                    ? 'bg-gray-800'
                    : 'bg-gray-100'
                  : theme === 'dark'
                  ? 'hover:bg-gray-800'
                  : 'hover:bg-gray-100'
              }`}
              title={t('navigation:about.us')}
            >
              <HelpCircle size={20} className="text-gray-400" />
              {!isCollapsed && <span>{t('navigation:about.us')}</span>}
            </a>
          </li>
        </ul>
      </nav>
      <div className={`mt-auto p-4 border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} space-y-4`}> 
        {isCollapsed ? (
          <button
            className={`w-full ${theme === 'dark' ? 'bg-gray-800 hover:bg-gray-700 text-gray-300' : 'bg-gray-100 hover:bg-gray-200 text-gray-600'} py-2 rounded-md flex items-center justify-center`}
            title={t('navigation:request.support')}
          >
            <ClipboardEdit size={18} />
          </button>
        ) : (
          <button className="w-full bg-orange-500 text-white py-2 px-4 rounded-md flex items-center justify-center space-x-2">
            <ClipboardEdit size={16} />
            <span>{t('navigation:request.support')}</span>
          </button>
        )}
        <ThemeToggle compact={isCollapsed} />
        <LanguageToggle compact={isCollapsed} />
      </div>
    </div>
  );
};

export default Sidebar;
