import React from 'react';
import { Moon, Sun } from 'lucide-react';
import { useTheme } from '@/shared/lib/ThemeContext';

interface ThemeToggleProps {
  compact?: boolean;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ compact = false }) => {
  const { theme, toggleTheme } = useTheme();

  if (compact) {
    const icon = theme === 'dark' ? <Sun size={18} className="text-gray-300" /> : <Moon size={18} className="text-gray-400" />;
    return (
      <button
        onClick={toggleTheme}
        aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
        className={`w-full flex items-center justify-center p-2 rounded-md transition ${
          theme === 'dark' ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-100 hover:bg-gray-200'
        }`}
      >
        {icon}
      </button>
    );
  }

  const statusLabel = theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode';

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        <Moon size={20} className="text-gray-400" />
        <span>Dark mode</span>
      </div>
      <button
        onClick={toggleTheme}
        aria-label={statusLabel}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${
          theme === 'dark' ? 'bg-orange-500' : 'bg-gray-600'
        }`}
      >
        <span
          className={`inline-block h-5 w-5 transform rounded-full bg-white transition-transform ${
            theme === 'dark' ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );
};

export default ThemeToggle;
