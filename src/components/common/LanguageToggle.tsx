import React from 'react';
import { Globe } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/lib/ThemeContext';

const FLAG_CLASSES = {
  vi: 'fi fi-vn',
  en: 'fi fi-gb',
} as const;

type LanguageCode = keyof typeof FLAG_SOURCES;
// const DISABLED_LANGUAGES = new Set<LanguageCode>([]);
const DISABLED_LANGUAGES = new Set<LanguageCode>(['en']);

interface LanguageToggleProps {
  compact?: boolean;
}

const LanguageToggle: React.FC<LanguageToggleProps> = ({ compact = false }) => {
  const { i18n, t } = useTranslation();
  const { theme } = useTheme();
  const language = i18n.language as LanguageCode;

  const altLabels: Record<LanguageCode, string> = {
    vi: t('common:vietnamese'),
    en: t('common:english'),
  };

  const handleSelect = (targetLanguage: LanguageCode) => {
    if (targetLanguage === language || DISABLED_LANGUAGES.has(targetLanguage)) {
      return;
    }

    i18n.changeLanguage(targetLanguage);
  };

  const getButtonClasses = (targetLanguage: LanguageCode) => {
    const isActive = language === targetLanguage;
    const isDisabled = DISABLED_LANGUAGES.has(targetLanguage) && !isActive;
    const baseClasses = 'flex items-center justify-center rounded-md p-2 transition border';

    if (isActive) {
      return `${baseClasses} bg-orange-500 text-white border-transparent`;
    }

    if (theme === 'dark') {
      return `${baseClasses} bg-gray-800 text-gray-200 border-gray-700 ${
        isDisabled ? 'opacity-40 cursor-not-allowed' : 'hover:bg-gray-700'
      }`;
    }

    return `${baseClasses} bg-gray-100 text-gray-700 border-gray-200 ${
      isDisabled ? 'opacity-40 cursor-not-allowed' : 'hover:bg-gray-200'
    }`;
  };

  if (compact) {
    return (
      <button
        type="button"
        onClick={() => handleSelect(language === 'vi' ? 'en' : 'vi')}
        aria-label={altLabels[language]}
        className={`${getButtonClasses(language)} w-full`}
        disabled={DISABLED_LANGUAGES.has(language === 'vi' ? 'en' : 'vi')}
      >
        <span
          className={`${FLAG_CLASSES[language]} rounded-full`}
          title={altLabels[language]}
          aria-label={altLabels[language]}
          style={{ width: '1.25rem', height: '1.25rem' }}
        />
      </button>
    );
  }

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        <Globe size={20} className="text-gray-400" />
        <span>{t('common:language')}</span>
      </div>
      <div className="flex space-x-2">
        {(['vi', 'en'] as LanguageCode[]).map(code => (
          <button
            key={code}
            type="button"
            onClick={() => handleSelect(code)}
            aria-pressed={language === code}
            aria-label={altLabels[code]}
            className={getButtonClasses(code)}
            disabled={DISABLED_LANGUAGES.has(code) && language !== code}
          >
            <span
              className={`${FLAG_CLASSES[code]} rounded-full`}
              title={altLabels[code]}
              aria-label={altLabels[code]}
              style={{ width: '1.25rem', height: '1.25rem' }}
            />
          </button>
        ))}
      </div>
    </div>
  );
};

export default LanguageToggle;
