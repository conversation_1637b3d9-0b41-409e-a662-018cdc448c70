import React, { useState, useMemo, useRef, useEffect } from 'react';
import { Search, ChevronDown, Maximize2, Minimize2, Eye, X, FilterX, Ship, Navigation } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { NavaidFilterType, StatusFilterType } from '@/pages/DashboardPage';
import { useFilters, VesselType } from '@/shared/lib/FiltersContext';
import MultiSelect from '@/components/forms/filters/MultiSelect';
import type { Option } from '@/components/forms/filters/MultiSelect';
import {
  VESSEL_CATEGORIES,
  VESSEL_TYPE_DEFINITIONS,
} from '@/domains/vessels/types/vesselTypeDefinitions';
import { VesselInfo } from '@/domains/vessels/types/vessel';
import { useVesselStore } from '@/domains/vessels/store/useVesselStore';
import { useAtonStore } from '@/domains/atons/store/useAtonStore';
import { getAtonTypeDefinition } from '@/domains/atons/types/atonTypeDefinitions';

// Unified search result interface
interface SearchResult {
  id: string;
  type: 'vessel' | 'aton';
  name: string;
  mmsi: string;
  lat: number;
  lon: number;
  // Vessel-specific properties
  vesselType?: string;
  vesselTypeLabel?: string;
  // ATON-specific properties
  atonType?: string;
  atonTypeLabel?: string;
  isVirtual?: boolean;
  channel?: string;
}

interface HeaderProps {
  selectedDate: string;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedFilter: NavaidFilterType;
  setSelectedFilter: (filter: NavaidFilterType) => void;
  selectedStatusFilter: StatusFilterType;
  setSelectedStatusFilter: (filter: StatusFilterType) => void;
  isFullscreen: boolean;
  onToggleFullscreen: () => void;
  isFocusMode: boolean;
  onToggleFocusMode: () => void;
  vessels: VesselInfo[];
  onSelectVessel?: (lngLat: [number, number]) => void;
}

const Header: React.FC<HeaderProps> = ({
  selectedDate,
  searchQuery,
  setSearchQuery,
  isFullscreen,
  onToggleFullscreen,
  isFocusMode,
  onToggleFocusMode,
  vessels,
  onSelectVessel,
}) => {
  const { t } = useTranslation();
  const { filters, setFilters, clearFilters } = useFilters();
  const setSelectedVessel = useVesselStore((state) => state.setSelectedVessel);
  const setSelectedAton = useAtonStore((state) => state.setSelectedAton);
  const atonList = useAtonStore((state) => state.atonList);
  
  // Search state
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Convert vessels and ATONs to unified search results
  const searchResults = useMemo(() => {
    if (!searchQuery.trim()) return [];
    
    const query = searchQuery.toLowerCase().trim();
    const results: SearchResult[] = [];

    // Filter vessels
    const filteredVessels = vessels.filter(vessel => {
      const mmsi = vessel.mmsi?.toLowerCase() || '';
      const name = vessel.name?.toLowerCase() || '';
      return mmsi.includes(query) || name.includes(query);
    });

    // Convert vessels to search results
    filteredVessels.forEach(vessel => {
      results.push({
        id: vessel.mmsi,
        type: 'vessel',
        name: vessel.name || 'Unknown Vessel',
        mmsi: vessel.mmsi,
        lat: vessel.lat,
        lon: vessel.lon,
        vesselType: vessel.vesselType,
        vesselTypeLabel: vessel.vesselTypeLabel,
      });
    });

    // Filter ATONs
    const filteredAtons = atonList.filter(aton => {
      const mmsi = aton.mmsi?.toLowerCase() || '';
      const name = aton.name?.toLowerCase() || '';
      return mmsi.includes(query) || name.includes(query);
    });

    // Convert ATONs to search results
    filteredAtons.forEach(aton => {
      const typeDefinition = getAtonTypeDefinition(aton.aidtype || undefined, aton.isVirtual);
      results.push({
        id: aton.id,
        type: 'aton',
        name: aton.name || 'Unknown ATON',
        mmsi: aton.mmsi,
        lat: aton.lat,
        lon: aton.lon,
        atonType: aton.aidtype || undefined,
        atonTypeLabel: typeDefinition?.label || aton.aidtype || '—',
        isVirtual: aton.isVirtual,
        channel: aton.channel || undefined,
      });
    });

    // Sort results: vessels first, then ATONs, then by name
    return results.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'vessel' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    }).slice(0, 10); // Limit to 10 results
  }, [vessels, atonList, searchQuery]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    setIsSearchOpen(value.length > 0);
    setFocusedIndex(-1);
  };

  // Handle result selection
  const handleResultSelect = (result: SearchResult) => {
    setSearchQuery('');
    setIsSearchOpen(false);
    setFocusedIndex(-1);
    
    if (result.type === 'vessel') {
      // Select the vessel
      setSelectedVessel(result.mmsi);
    } else if (result.type === 'aton') {
      // Select the ATON
      setSelectedAton(result.id);
    }
    
    // Center the map on the selected entity
    if (onSelectVessel && Number.isFinite(result.lon) && Number.isFinite(result.lat)) {
      onSelectVessel([result.lon, result.lat]);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isSearchOpen || searchResults.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev => 
          prev < searchResults.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => 
          prev > 0 ? prev - 1 : searchResults.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (focusedIndex >= 0 && focusedIndex < searchResults.length) {
          handleResultSelect(searchResults[focusedIndex]);
        }
        break;
      case 'Escape':
        setIsSearchOpen(false);
        setFocusedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsSearchOpen(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
    setIsSearchOpen(false);
    setFocusedIndex(-1);
    inputRef.current?.focus();
  };

  const filtersContent = (
    <div className="flex items-center gap-3">
      <MultiSelect
        label="Loại tàu"
        options={vesselTypeOptions}
        value={filters.vesselTypes as string[]}
        onChange={(v: string[]) => setFilters({ vesselTypes: v as unknown as never })}
      />
      <MultiSelect
        label="Trạng thái tàu"
        options={vesselNavStatusOptions}
        value={filters.vesselNavStatuses as string[]}
        onChange={(v: string[]) => setFilters({ vesselNavStatuses: v as unknown as never })}
      />
      <MultiSelect
        label="Loại thiết bị"
        options={deviceTypeOptions}
        value={filters.deviceTypes as unknown as string[]}
        onChange={(v: string[]) => setFilters({ deviceTypes: v as unknown as never })}
      />
      <MultiSelect
        label="Trạng thái thiết bị"
        options={deviceStatusOptions}
        value={filters.deviceStatuses as unknown as string[]}
        onChange={(v: string[]) => setFilters({ deviceStatuses: v as unknown as never })}
      />
      {(filters.vesselTypes.length > 0 || filters.vesselNavStatuses.length > 0 || filters.deviceTypes.length > 0 || filters.deviceStatuses.length > 0) && (
        <button className="px-3 py-2 rounded-md text-sm bg-red-600 hover:bg-red-700 text-white" onClick={clearFilters}>
          <FilterX size={16} />
        </button>
      )}
    </div>
  );

  return (
    <div className="bg-gray-900 border-b border-gray-700 p-4">
      <div className="flex items-center gap-4 w-full">
        <div className="flex items-center space-x-2">
          <h2 className="text-xl font-semibold">{t('dashboard:dashboard')}:</h2>
          <div className="relative">
            <button className="flex items-center space-x-1 text-xl text-orange-500 font-semibold">
              <span>Điện Gió Tân Thuận</span>
              <ChevronDown size={20} />
            </button>
          </div>
        </div>

        <div className="flex-1 flex justify-center">
          <div ref={searchRef} className="relative w-full max-w-xl">
            <input
              ref={inputRef}
              type="text"
              placeholder={t('dashboard:search.placeholder')}
              className="w-full py-2 pl-10 pr-10 bg-white border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none text-gray-900 placeholder-gray-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white dark:placeholder-gray-400"
              value={searchQuery}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onFocus={() => searchQuery.length > 0 && setIsSearchOpen(true)}
            />
            <Search
              size={18}
              className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-500 dark:text-gray-400"
            />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X size={16} />
              </button>
            )}

            {/* Search Results Dropdown */}
            {isSearchOpen && searchResults.length > 0 && (
              <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-80 overflow-y-auto dark:bg-gray-800 dark:border-gray-600">
                {searchResults.map((result, index) => (
                  <button
                    key={`${result.type}-${result.id}`}
                    onClick={() => handleResultSelect(result)}
                    className={`w-full text-left px-4 py-3 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none dark:hover:bg-gray-700 dark:focus:bg-gray-700 ${
                      index === focusedIndex ? 'bg-gray-50 dark:bg-gray-700' : ''
                    } ${index !== searchResults.length - 1 ? 'border-b border-gray-100 dark:border-gray-700' : ''}`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          {result.type === 'vessel' ? (
                            <Ship size={16} className="text-blue-500" />
                          ) : (
                            <Navigation size={16} className="text-orange-500" />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-gray-900 dark:text-white truncate">
                            {result.name}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            MMSI: {result.mmsi}
                          </div>
                          {result.type === 'vessel' && result.vesselTypeLabel && (
                            <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                              {result.vesselTypeLabel}
                            </div>
                          )}
                          {result.type === 'aton' && (
                            <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                              {result.atonTypeLabel} • {result.isVirtual ? 'Ảo' : 'Thực'}
                              {result.channel && ` • AIS Class: Class ${result.channel}`}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 text-right flex-shrink-0 ml-2">
                        <div>Lat: {Number.isFinite(result.lat) ? result.lat.toFixed(4) : '—'}</div>
                        <div>Lon: {Number.isFinite(result.lon) ? result.lon.toFixed(4) : '—'}</div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}

            {/* No results message */}
            {isSearchOpen && searchQuery && searchResults.length === 0 && (
              <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg dark:bg-gray-800 dark:border-gray-600">
                <div className="px-4 py-3 text-gray-600 dark:text-gray-400 text-center">
                  Không tìm thấy tàu hoặc thiết bị nào
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-3">
          {filtersContent}

          <div className="flex items-center space-x-2">
            <button
              type="button"
              className={`bg-gray-800 p-2 rounded-md transition ${isFullscreen ? 'ring-2 ring-blue-500 bg-gray-700' : ''}`}
              onClick={onToggleFullscreen}
              aria-pressed={isFullscreen}
              title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
            >
              {isFullscreen ? (
                <Minimize2 size={18} className="text-gray-200" />
              ) : (
                <Maximize2 size={18} className="text-gray-400" />
              )}
            </button>
            <button
              type="button"
              className={`bg-gray-800 p-2 rounded-md transition ${isFocusMode ? 'ring-2 ring-blue-500 bg-gray-700' : ''}`}
              onClick={onToggleFocusMode}
              aria-pressed={isFocusMode}
              title={isFocusMode ? 'Exit focus mode' : 'Enter focus mode'}
            >
              {isFocusMode ? (
                <Eye size={18} className="text-red-500 drop-shadow-lg shadow-lg border border-white/20 rounded p-0.5" />
              ) : (
                <Eye size={18} className="text-gray-400" />
              )}
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <div className="text-lg font-semibold">{selectedDate}</div>
            {isFocusMode && (
              <span className="px-2 py-1 text-xs font-semibold bg-blue-600 text-white rounded-full uppercase tracking-wide">
                Focus Mode
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;

// Options
const categoryColorMap = Object.values(VESSEL_TYPE_DEFINITIONS).reduce<Record<VesselType, string>>((acc, def) => {
  const cat = def.category as VesselType;
  if (!acc[cat]) {
    acc[cat] = def.colorClass;
  }
  return acc;
}, {} as Record<VesselType, string>);

const categoryLabelMap = Object.values(VESSEL_TYPE_DEFINITIONS).reduce<Record<VesselType, string>>((acc, def) => {
  const cat = def.category as VesselType;
  if (!acc[cat]) {
    acc[cat] = def.label;
  }
  return acc;
}, {} as Record<VesselType, string>);

const vesselTypeOptions: Option[] = VESSEL_CATEGORIES.map((category) => ({
  label: categoryLabelMap[category] ?? category.charAt(0).toUpperCase() + category.slice(1),
  value: category,
  colorClass: categoryColorMap[category] ?? 'bg-gray-500',
}));

const vesselNavStatusOptions: Option[] = [
  { label: 'Under way', value: 'under_way', colorClass: 'bg-green-500' },
  { label: 'At anchor', value: 'at_anchor' },
  { label: 'Not under command', value: 'not_under_command' },
  { label: 'Restricted', value: 'restricted' },
  { label: 'Constrained by draft', value: 'constrained' },
  { label: 'Moored', value: 'moored' },
  { label: 'Aground', value: 'aground' },
  { label: 'Fishing', value: 'fishing' },
  { label: 'Sailing', value: 'sailing' },
  { label: 'Undefined', value: 'undefined' },
];

const deviceTypeOptions: Option[] = [
  { label: 'SEALITE', value: 'sealite', colorClass: 'bg-blue-500' },
  { label: 'LIGHTHOUSE', value: 'lighthouse', colorClass: 'bg-purple-500' },
  { label: 'BEACON', value: 'beacon', colorClass: 'bg-yellow-500' },
  { label: 'MARKER', value: 'marker', colorClass: 'bg-red-500' },
];

const deviceStatusOptions: Option[] = [
  { label: 'Operational', value: 'operational', colorClass: 'bg-green-500' },
  { label: 'Maintenance', value: 'maintenance', colorClass: 'bg-yellow-500' },
  { label: 'Offline', value: 'offline', colorClass: 'bg-red-500' },
];
