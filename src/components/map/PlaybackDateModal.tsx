import React, { useState } from 'react';
import { X, Calendar } from 'lucide-react';
import { useTheme } from '@/shared/lib/ThemeContext';

interface PlaybackDateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (fromDate: string, toDate: string) => void;
  vesselName?: string;
}

const PlaybackDateModal: React.FC<PlaybackDateModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  vesselName = 'Unknown Vessel'
}) => {
  const { theme } = useTheme();
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');

  // Set default dates (last 7 days)
  React.useEffect(() => {
    if (isOpen) {
      const today = new Date();
      const fromDate = new Date(today.getTime() - 3 * 24 * 60 * 60 * 1000);

      setToDate(today.toISOString().split('T')[0]);
      setFromDate(fromDate.toISOString().split('T')[0]);
    }
  }, [isOpen]);

  const handleConfirm = () => {
    if (fromDate && toDate) {
      // Convert from date to 00:00:00Z (start of day)
      const fromDateTime = `${fromDate}T00:00:00Z`;
      // Convert to date to 23:59:59Z (end of day)
      const toDateTime = `${toDate}T23:59:59Z`;

      onConfirm(fromDateTime, toDateTime);
      onClose();
    }
  };

  const handleCancel = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`rounded-xl border shadow-xl max-w-md w-full mx-4 ${theme === 'dark'
          ? 'bg-gray-900 border-gray-700'
          : 'bg-white border-gray-200'
        }`}>
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
          }`}>
          <div className="flex items-center space-x-3">
            <Calendar className={`w-5 h-5 ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'
              }`} />
            <h2 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
              Xem hành trình tàu
            </h2>
          </div>
          <button
            onClick={onClose}
            className={`p-1 rounded-md ${theme === 'dark'
                ? 'hover:bg-gray-800 text-gray-400'
                : 'hover:bg-gray-100 text-gray-500'
              }`}
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          <div>
            <p className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`}>
              Vui lòng chọn thời gian xem tàu: <span className="font-medium">{vesselName}</span>
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <label className={`block text-sm font-medium mb-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                Từ ngày
              </label>
              <input
                type="date"
                value={fromDate}
                onChange={(e) => setFromDate(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${theme === 'dark'
                    ? 'bg-gray-800 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                  }`}
                max={toDate || new Date().toISOString().split('T')[0]}
              />
            </div>

            <div>
              <label className={`block text-sm font-medium mb-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                Đến ngày
              </label>
              <input
                type="date"
                value={toDate}
                onChange={(e) => setToDate(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${theme === 'dark'
                    ? 'bg-gray-800 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                  }`}
                max={new Date().toISOString().split('T')[0]}
                min={fromDate}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              onClick={handleCancel}
              className={`flex-1 px-4 py-2 rounded-lg border font-medium transition-colors ${theme === 'dark'
                  ? 'border-gray-600 text-gray-300 hover:bg-gray-800'
                  : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
            >
              Hủy
            </button>
            <button
              onClick={handleConfirm}
              disabled={!fromDate || !toDate}
              className={`flex-1 px-4 py-2 rounded-lg font-medium transition-colors ${!fromDate || !toDate
                  ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
                }`}
            >
              Bắt đầu xem
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlaybackDateModal;
