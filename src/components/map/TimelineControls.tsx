import React from 'react';
import { ChevronLeft, ChevronRight, Clock } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const TimelineControls: React.FC = () => {
  const { t } = useTranslation();
  const timeSlots = ['08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00', '00:00'];
  
  return (
    <div className="bg-gray-800 border-t border-gray-700 p-4">
      <div className="flex justify-between items-center mb-4">
        <div className="flex space-x-4">
          <button className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-700 hover:bg-gray-600">
            <ChevronLeft size={18} />
          </button>
          <button className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-700 hover:bg-gray-600">
            <ChevronRight size={18} />
          </button>
        </div>
        
        <div className="flex space-x-4">
          <button className="px-3 py-1 bg-blue-600 rounded-full text-sm flex items-center">
            <Clock size={14} className="mr-1" />
            <span>{t('timeline:real.time')}</span>
          </button>
          <button className="px-3 py-1 bg-gray-700 rounded-full text-sm">{t('timeline:24h.view')}</button>
          <button className="px-3 py-1 bg-gray-700 rounded-full text-sm">{t('timeline:7day.view')}</button>
          <button className="px-3 py-1 bg-gray-700 rounded-full text-sm">{t('timeline:historical')}</button>
        </div>
      </div>
      
      <div className="flex justify-between mt-2">
        {timeSlots.map((time, index) => (
          <div key={index} className="text-center">
            <div className={`text-sm ${index === 3 ? 'text-blue-500 font-bold' : 'text-gray-400'}`}>{time}</div>
            <div className="h-1 w-full mt-1 flex items-center justify-center">
              <div className={`h-1 w-1 rounded-full ${index === 3 ? 'bg-blue-500' : 'bg-gray-600'}`}></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TimelineControls;