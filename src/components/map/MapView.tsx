import { useCallback, useEffect, useMemo, useRef } from 'react';
import maplibregl from 'maplibre-gl';
// import 'maplibre-gl/dist/maplibre-gl.css';

// MapLibre plugins
import "maplibre-theme/icons.lucide.css";
import "maplibre-theme/modern.css";

import BasemapsControl from 'maplibre-gl-basemaps';
import 'maplibre-gl-basemaps/lib/basemaps.css';

import { useVesselMapLayers } from '@/domains/vessels/hooks/useVesselMapLayers';
import { useAtonMapLayers } from '@/domains/atons/hooks/useAtonMapLayers';
import { useAreasLayer } from '@/domains/areas/hooks/useAreasLayer';
import SelectedVesselPanel from '@/components/panels/SelectedVesselPanel';
import SelectedAtonPanel from '@/components/panels/SelectedAtonPanel';
import { useVesselStore } from '@/domains/vessels/store/useVesselStore';
import { useAtonStore } from '@/domains/atons/store/useAtonStore';
import { createLayerControl } from './LayerControl';
import useMapInstance from '@/shared/hooks/useMapInstance';

interface MapViewProps {
  onMapReady?: (map: maplibregl.Map | null) => void;
}

export default function MapView({ onMapReady }: MapViewProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const selectedVesselId = useVesselStore((state) => state.selectedVesselId);
  const selectedAtonId = useAtonStore((state) => state.selectedAtonId);
  // keep hook simple; we don't need mapLoaded externally right now

  // Ho Chi Minh City coordinates (updated to match the sample)
  const lng = 107.06365282546393;
  const lat = 10.322612425576223;
  const zoom = 12;

  // Basemap control options
  const basemapOptions = useMemo(() => ({
    basemaps: [
      {
        id: 'eox-satellite',
        tiles: [
          'https://tiles.maps.eox.at/wmts/1.0.0/s2cloudless-2020_3857/default/g/{z}/{y}/{x}.jpg'
        ],
        sourceExtraParams: {
          tileSize: 256
        },
      },
      {
        id: 'openstreetmap',
        tiles: [
          'https://tile.openstreetmap.org/{z}/{x}/{y}.png'
        ],
        sourceExtraParams: {
          tileSize: 256
        },
      },
      {
        id: 'carto-voyager',
        tiles: [
          'https://a.basemaps.cartocdn.com/rastertiles/voyager_labels_under/{z}/{x}/{y}@1x.png',
          'https://b.basemaps.cartocdn.com/rastertiles/voyager_labels_under/{z}/{x}/{y}@1x.png',
          'https://c.basemaps.cartocdn.com/rastertiles/voyager_labels_under/{z}/{x}/{y}@1x.png',
          'https://d.basemaps.cartocdn.com/rastertiles/voyager_labels_under/{z}/{x}/{y}@1x.png',
        ],
        sourceExtraParams: {
          tileSize: 256
        },
      }
    ],
    initialBasemap: 'openstreetmap',
    expandDirection: 'right' as const
  }), []);

  const mapOptions = useMemo(() => ({
    style: {
      version: 8,
      sources: {},
      layers: []
    },
    center: [lng, lat] as [number, number],
    zoom,
    attributionControl: false,
  }), [lng, lat, zoom]);

  const handleMapError = useCallback((error: Error) => {
    console.error('MapLibre error:', error);
  }, []);

  const controls = useMemo(() => ([
    {
      factory: () => new maplibregl.NavigationControl({
        visualizePitch: true,
        visualizeRoll: true,
        showZoom: true,
        showCompass: true
      })
    },
    { factory: () => new maplibregl.FullscreenControl() },
    {
      factory: () => new maplibregl.GeolocateControl({
        positionOptions: { enableHighAccuracy: true },
        trackUserLocation: false
      })
    },
    { factory: () => new BasemapsControl(basemapOptions), position: 'bottom-left' as const },
    { factory: () => createLayerControl(), position: 'top-left' as const }
  ]), [basemapOptions]);

  const { map: mapInstance } = useMapInstance({
    containerRef: mapContainer,
    mapOptions,
    controls,
    onError: handleMapError,
  });

  useAreasLayer(mapInstance);
  useVesselMapLayers(mapInstance);
  useAtonMapLayers(mapInstance);

  useEffect(() => {
    if (mapInstance) {
      onMapReady?.(mapInstance);
    }

    return () => {
      onMapReady?.(null);
    };
  }, [mapInstance, onMapReady]);


  return (
    <div className="map-wrap w-full h-full relative">
      <div ref={mapContainer} className="map w-full h-full" style={{ minHeight: '400px' }} />
      {selectedVesselId && (
        <div className="absolute bottom-4 right-4 z-30" style={{ maxWidth: '20rem' }}>
          <SelectedVesselPanel />
        </div>
      )}
      {selectedAtonId && (
        <div className="absolute bottom-4 right-4 z-30" style={{ maxWidth: '20rem' }}>
          <SelectedAtonPanel />
        </div>
      )}
    </div>
  );
}
