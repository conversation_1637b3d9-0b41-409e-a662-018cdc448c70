import maplibregl from 'maplibre-gl';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { createRoot, Root } from 'react-dom/client';
import { VESSEL_LAYER_IDS } from '@/domains/vessels/hooks/useVesselMapLayers';
import { ATON_LAYER_ID } from '@/domains/atons/hooks/useAtonMapLayers';
import { AREAS_LAYER_ID } from '@/domains/areas/hooks/useAreasLayer';
import { Layers, X, CheckSquare, Square } from 'lucide-react';
import { useTheme } from '@/shared/lib/ThemeContext';

type LayerGroupKey = 'all' | 'vessels' | 'atons' | 'areas';

function setVisible(map: maplibregl.Map, layerIds: string[], visible: boolean) {
  const visibility = visible ? 'visible' : 'none';
  layerIds.forEach((id) => {
    if (map.getLayer(id)) {
      map.setLayoutProperty(id, 'visibility', visibility);
    }
  });
}

function getVisible(map: maplibregl.Map, layerIds: string[]): boolean | undefined {
  let anyFound = false;
  let allVisible = true;
  for (const id of layerIds) {
    const layer = map.getLayer(id);
    if (!layer) continue;
    anyFound = true;
    const vis = map.getLayoutProperty(id, 'visibility') as string | undefined;
    if (vis === 'none') allVisible = false;
  }
  return anyFound ? allVisible : undefined;
}

function useLayerState(map: maplibregl.Map | null) {
  const layerIds = useMemo(() => ({
    vessels: VESSEL_LAYER_IDS,
    atons: [ATON_LAYER_ID],
    areas: [AREAS_LAYER_ID],
  }), []);

  const compute = useCallback(() => ({
    vessels: map ? getVisible(map, layerIds.vessels) !== false : true,
    atons: map ? getVisible(map, layerIds.atons) !== false : true,
    areas: map ? getVisible(map, layerIds.areas) !== false : true,
  }), [map, layerIds]);

  const [state, setState] = useState(() => compute());

  useEffect(() => {
    if (!map) return;
    setState(compute());
  }, [map, compute]);

  const setGroup = (key: Exclude<LayerGroupKey, 'all'>, visible: boolean) => {
    if (!map) return;
    setVisible(map, layerIds[key], visible);
    setState((s) => ({ ...s, [key]: visible }));
  };

  const setAll = (visible: boolean) => {
    if (!map) return;
    setVisible(map, layerIds.vessels, visible);
    setVisible(map, layerIds.atons, visible);
    setVisible(map, layerIds.areas, visible);
    setState({ vessels: visible, atons: visible, areas: visible });
  };

  return { state, setGroup, setAll };
}

function Checkbox({ checked }: { checked: boolean }) {
  const Icon = checked ? CheckSquare : Square;
  return (
    <span className="inline-flex w-5 h-5 items-center justify-center shrink-0">
      <Icon size={16} className={checked ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'} />
    </span>
  );
}

function ControlUI({ map }: { map: maplibregl.Map }) {
  const { state, setGroup, setAll } = useLayerState(map);
  const [open, setOpen] = useState(false);
  const { theme } = useTheme();

  const allChecked = state.vessels && state.atons && state.areas;

  return (
    <div className="pointer-events-auto select-none whitespace-nowrap">
      {!open && (
        <button
          aria-label="Lớp bản đồ"
          title="Lớp bản đồ"
          className="w-9 h-9 rounded-md bg-white/95 dark:bg-slate-700/90 text-gray-700 dark:text-white shadow hover:bg-gray-50 dark:hover:bg-slate-600 flex items-center justify-center border border-gray-200 dark:border-slate-600"
          onClick={() => setOpen(true)}
        >
          <Layers size={18} className="inline-block" />
        </button>
      )}

      {open && (
        <div className="w-64 rounded-md bg-white/95 dark:bg-slate-800/95 text-gray-800 dark:text-white shadow-lg border border-gray-200 dark:border-slate-700">
          <div className="flex items-center justify-between px-3 py-2 border-b border-gray-200 dark:border-slate-700">
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-200">
              <Layers size={16} className="inline-block" />
              <span>Lớp bản đồ</span>
            </div>
            <button aria-label="Đóng" onClick={() => setOpen(false)} className="p-1 rounded hover:bg-gray-100 dark:hover:bg-slate-700 text-gray-600 dark:text-gray-300">
              <X size={16} className="inline-block" />
            </button>
          </div>

          <button
            className="w-full flex items-center gap-2 px-3 py-2 hover:bg-gray-50 dark:hover:bg-slate-700/60 text-sm text-gray-700 dark:text-gray-200"
            onClick={() => setAll(!allChecked)}
          >
            <Checkbox checked={allChecked} />
            <span>Bật/tắt toàn bộ</span>
          </button>

          <div className="py-1">
            <button className="w-full flex items-center gap-2 px-3 py-2 hover:bg-gray-50 dark:hover:bg-slate-700/60 text-sm text-gray-700 dark:text-gray-200" onClick={() => setGroup('vessels', !state.vessels)}>
              <Checkbox checked={state.vessels} />
              <span>Lớp tàu</span>
            </button>
            <button className="w-full flex items-center gap-2 px-3 py-2 hover:bg-gray-50 dark:hover:bg-slate-700/60 text-sm text-gray-700 dark:text-gray-200" onClick={() => setGroup('atons', !state.atons)}>
              <Checkbox checked={state.atons} />
              <span>Lớp phao AtoN/đèn hàng hải</span>
            </button>
            <button className="w-full flex items-center gap-2 px-3 py-2 hover:bg-gray-50 dark:hover:bg-slate-700/60 text-sm text-gray-700 dark:text-gray-200" onClick={() => setGroup('areas', !state.areas)}>
              <Checkbox checked={state.areas} />
              <span>Lớp vùng</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export function createLayerControl() {
  class LayerControl implements maplibregl.IControl {
    private container: HTMLDivElement | undefined;
    private root: Root | undefined;
    onAdd(map: maplibregl.Map) {
      this.container = document.createElement('div');
      // Use only maplibregl-ctrl to avoid the tight button sizing of maplibregl-ctrl-group
      this.container.className = 'maplibregl-ctrl';
      this.root = createRoot(this.container);
      this.root.render(React.createElement(ControlUI, { map }));
      return this.container;
    }
    onRemove() {
      if (this.root) {
        this.root.unmount();
        this.root = undefined;
      }
      if (this.container && this.container.parentNode) {
        this.container.parentNode.removeChild(this.container);
      }
      this.container = undefined;
    }
  }
  return new LayerControl();
}

export default createLayerControl;


