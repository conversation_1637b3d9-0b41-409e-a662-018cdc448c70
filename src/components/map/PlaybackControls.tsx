import React, { useEffect, useRef, useCallback } from 'react';
import { Play, Pause, SkipBack, SkipForward, X, RotateCcw, RotateCw } from 'lucide-react';
import { useTheme } from '@/shared/lib/ThemeContext';
import { useVesselStore } from '@/domains/vessels/store/useVesselStore';
import * as turf from '@turf/turf';

const PlaybackControls: React.FC = () => {
  const { theme } = useTheme();
  const {
    isPlaybackMode,
    playbackVesselId,
    playbackData,
    playbackCurrentIndex,
    playbackIsPlaying,
    playbackSpeed,
    vesselMap,
    setPlaybackCurrentIndex,
    setPlaybackIsPlaying,
    setPlaybackSpeed,
    stopPlayback,
    updatePlaybackVesselPosition,
  } = useVesselStore();

  const animationRef = useRef<number | null>(null);
  const routeRef = useRef<GeoJSON.Feature<GeoJSON.LineString> | null>(null);
  const arcRef = useRef<number[][]>([]);
  const counterRef = useRef<number>(0);
  const stepsRef = useRef<number>(500);

  const playbackVessel = playbackVesselId ? vesselMap[playbackVesselId] : null;
  const currentLog = playbackData[playbackCurrentIndex];
  const progress = arcRef.current.length > 0 ? (counterRef.current / (arcRef.current.length - 1)) * 100 : 0;

  // Helper function to update vessel position at a specific counter
  const updateVesselPositionAtCounter = useCallback((counter: number) => {
    if (arcRef.current.length === 0) return;

    const currentCoords = arcRef.current[counter];
    const nextCoords = arcRef.current[Math.min(counter + 1, arcRef.current.length - 1)];
    
    // Calculate bearing for vessel rotation
    const bearing = turf.bearing(
      turf.point(currentCoords),
      turf.point(nextCoords)
    );

    // Update the vessel position
    updatePlaybackVesselPosition(currentCoords[1], currentCoords[0], bearing);
  }, [updatePlaybackVesselPosition]);

  // Create smooth route from playback data
  const createSmoothRoute = useCallback(() => {
    if (playbackData.length < 2) return null;

    const coordinates = playbackData
      .filter(log => log.position && log.position.lat && log.position.lon)
      .map(log => [parseFloat(log.position.lon), parseFloat(log.position.lat)]);

    if (coordinates.length < 2) return null;

    const route = turf.lineString(coordinates);
    const lineDistance = turf.length(route, { units: 'kilometers' });
    
    // Create smooth arc with interpolated points
    const arc: number[][] = [];
    const steps = Math.min(500, Math.max(50, Math.floor(lineDistance * 10))); // Dynamic steps based on distance
    stepsRef.current = steps;

    for (let i = 0; i <= lineDistance; i += lineDistance / steps) {
      const segment = turf.along(route, i, { units: 'kilometers' });
      arc.push(segment.geometry.coordinates);
    }

    arcRef.current = arc;
    routeRef.current = route;
    return route;
  }, [playbackData]);

  // Smooth animation function using requestAnimationFrame
  const animate = useCallback(() => {
    if (!playbackIsPlaying || arcRef.current.length === 0) return;

    const arc = arcRef.current;
    const counter = counterRef.current;

    if (counter >= arc.length) {
      setPlaybackIsPlaying(false);
      return;
    }

    // Update the current position based on counter
    const currentCoords = arc[counter];
    const nextCoords = arc[Math.min(counter + 1, arc.length - 1)];
    
    // Calculate bearing for vessel rotation
    const bearing = turf.bearing(
      turf.point(currentCoords),
      turf.point(nextCoords)
    );

    // Update the vessel position smoothly with rotation
    updatePlaybackVesselPosition(currentCoords[1], currentCoords[0], bearing);
    
    // Update the playback index for progress tracking
    setPlaybackCurrentIndex(counter);

    counterRef.current = counter + 1;

    // Continue animation with speed control
    if (playbackIsPlaying && counter < arc.length) {
      // Use a more reliable timing approach
      const delay = Math.max(16, 1000 / playbackSpeed); // Minimum 16ms for 60fps
      animationRef.current = setTimeout(() => {
        animationRef.current = requestAnimationFrame(animate);
      }, delay);
    }
  }, [playbackIsPlaying, setPlaybackCurrentIndex, setPlaybackIsPlaying, updatePlaybackVesselPosition, playbackSpeed]);

  // Handle playback animation
  useEffect(() => {
    if (playbackIsPlaying && playbackData.length > 0) {
      // Create smooth route if not exists
      if (!routeRef.current) {
        createSmoothRoute();
      }
      
      // Start animation
      animationRef.current = requestAnimationFrame(animate);
    } else {
      if (animationRef.current) {
        clearTimeout(animationRef.current);
        animationRef.current = null;
      }
    }

    return () => {
      if (animationRef.current) {
        clearTimeout(animationRef.current);
        animationRef.current = null;
      }
    };
  }, [playbackIsPlaying, playbackData.length, createSmoothRoute, animate]);

  // Reset animation when playback data changes
  useEffect(() => {
    if (playbackData.length > 0) {
      counterRef.current = 0;
      routeRef.current = null;
      arcRef.current = [];
      createSmoothRoute();
    }
  }, [playbackData, createSmoothRoute]);

  const handlePlayPause = () => {
    setPlaybackIsPlaying(!playbackIsPlaying);
  };

  const handleSkipBack = () => {
    const newCounter = Math.max(0, counterRef.current - Math.floor(stepsRef.current / 10));
    counterRef.current = newCounter;
    updateVesselPositionAtCounter(newCounter);
    setPlaybackCurrentIndex(newCounter);
  };

  const handleSkipForward = () => {
    const newCounter = Math.min(arcRef.current.length - 1, counterRef.current + Math.floor(stepsRef.current / 10));
    counterRef.current = newCounter;
    updateVesselPositionAtCounter(newCounter);
    setPlaybackCurrentIndex(newCounter);
  };

  const handleGoToStart = () => {
    counterRef.current = 0;
    updateVesselPositionAtCounter(0);
    setPlaybackCurrentIndex(0);
    setPlaybackIsPlaying(false);
  };

  const handleGoToEnd = () => {
    const endCounter = arcRef.current.length - 1;
    counterRef.current = endCounter;
    updateVesselPositionAtCounter(endCounter);
    setPlaybackCurrentIndex(endCounter);
    setPlaybackIsPlaying(false);
  };

  const handleSpeedChange = (speed: number) => {
    setPlaybackSpeed(speed);
  };

  const handleStop = () => {
    stopPlayback();
  };

  const formatTime = (timestamp?: string) => {
    if (!timestamp) return '—';
    return new Date(timestamp).toLocaleString();
  };

  if (!isPlaybackMode || !playbackVessel) {
    return null;
  }

  return (
    <div className={`fixed bottom-4 left-1/2 transform -translate-x-1/2 z-40 rounded-xl border shadow-xl max-w-2xl w-full mx-4 ${
      theme === 'dark' 
        ? 'bg-gray-900 border-gray-700' 
        : 'bg-white border-gray-200'
    }`}>
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${
              playbackIsPlaying ? 'bg-green-500' : 'bg-gray-400'
            }`} />
            <h3 className={`font-semibold ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              Đang xem hành trình tàu: {playbackVessel.name || 'Unknown Vessel'}
            </h3>
          </div>
          <button
            onClick={handleStop}
            className={`p-1 rounded-md ${
              theme === 'dark' 
                ? 'hover:bg-gray-800 text-gray-400' 
                : 'hover:bg-gray-100 text-gray-500'
            }`}
          >
            <X size={18} />
          </button>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className={`w-full h-2 rounded-full ${
            theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
          }`}>
            <div
              className="h-2 bg-blue-500 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span>{counterRef.current + 1} / {arcRef.current.length}</span>
            <span>{formatTime(currentLog?.timestamp)}</span>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {/* Go to Start */}
            <button
              onClick={handleGoToStart}
              disabled={counterRef.current === 0}
              className={`p-2 rounded-md ${
                counterRef.current === 0
                  ? 'text-gray-400 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'hover:bg-gray-800 text-gray-300'
                    : 'hover:bg-gray-100 text-gray-600'
              }`}
              title="Go to Start"
            >
              <RotateCcw size={18} />
            </button>

            {/* Skip Back */}
            <button
              onClick={handleSkipBack}
              disabled={counterRef.current === 0}
              className={`p-2 rounded-md ${
                counterRef.current === 0
                  ? 'text-gray-400 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'hover:bg-gray-800 text-gray-300'
                    : 'hover:bg-gray-100 text-gray-600'
              }`}
              title="Skip Back"
            >
              <SkipBack size={18} />
            </button>

            {/* Play/Pause */}
            <button
              onClick={handlePlayPause}
              className={`p-2 rounded-md ${
                theme === 'dark'
                  ? 'bg-blue-600 hover:bg-blue-700 text-white'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
              title={playbackIsPlaying ? 'Pause' : 'Play'}
            >
              {playbackIsPlaying ? <Pause size={18} /> : <Play size={18} />}
            </button>

            {/* Skip Forward */}
            <button
              onClick={handleSkipForward}
              disabled={counterRef.current >= arcRef.current.length - 1}
              className={`p-2 rounded-md ${
                counterRef.current >= arcRef.current.length - 1
                  ? 'text-gray-400 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'hover:bg-gray-800 text-gray-300'
                    : 'hover:bg-gray-100 text-gray-600'
              }`}
              title="Skip Forward"
            >
              <SkipForward size={18} />
            </button>

            {/* Go to End */}
            <button
              onClick={handleGoToEnd}
              disabled={counterRef.current >= arcRef.current.length - 1}
              className={`p-2 rounded-md ${
                counterRef.current >= arcRef.current.length - 1
                  ? 'text-gray-400 cursor-not-allowed'
                  : theme === 'dark'
                    ? 'hover:bg-gray-800 text-gray-300'
                    : 'hover:bg-gray-100 text-gray-600'
              }`}
              title="Go to End"
            >
              <RotateCw size={18} />
            </button>
          </div>

          {/* Speed Control */}
          <div className="flex items-center space-x-2">
            <span className={`text-sm ${
              theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
            }`}>
              Speed:
            </span>
            <div className="flex space-x-1">
              {[0.5, 1, 2, 4].map((speed) => (
                <button
                  key={speed}
                  onClick={() => handleSpeedChange(speed)}
                  className={`px-2 py-1 text-xs rounded ${
                    playbackSpeed === speed
                      ? 'bg-blue-500 text-white'
                      : theme === 'dark'
                        ? 'hover:bg-gray-800 text-gray-300'
                        : 'hover:bg-gray-100 text-gray-600'
                  }`}
                >
                  {speed}x
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlaybackControls;
