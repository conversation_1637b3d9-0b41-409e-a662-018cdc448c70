import React, { useEffect, useRef, useState } from 'react';

export interface Option {
  label: string;
  value: string;
  colorClass?: string; // optional tailwind color dot class
}

interface MultiSelectProps {
  label: string;
  options: Option[];
  value: string[];
  onChange: (next: string[]) => void;
}

const MultiSelect: React.FC<MultiSelectProps> = ({ label, options, value, onChange }) => {
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const onDocClick = (e: MouseEvent) => {
      if (ref.current && !ref.current.contains(e.target as Node)) setOpen(false);
    };
    document.addEventListener('click', onDocClick);
    return () => document.removeEventListener('click', onDocClick);
  }, []);

  const toggle = (val: string) => {
    const set = new Set(value);
    if (set.has(val)) set.delete(val); else set.add(val);
    onChange(Array.from(set));
  };

  const isActive = value.length > 0;

  return (
    <div className="relative" ref={ref}>
      <button
        className={`px-3 py-2 rounded-md text-sm flex items-center space-x-2 ${isActive ? 'bg-blue-600' : 'bg-gray-800 hover:bg-gray-700'}`}
        onClick={() => setOpen(o => !o)}
      >
        <span>{label}</span>
        {isActive && <span className="text-xs bg-black/20 px-2 py-0.5 rounded">{value.length}</span>}
      </button>

      {open && (
        <div className="absolute mt-2 z-20 w-64 bg-gray-900 border border-gray-700 rounded-md shadow-lg p-2">
          <div className="max-h-64 overflow-y-auto space-y-1">
            {options.map(opt => (
              <label key={opt.value} className="flex items-center space-x-2 px-2 py-2 rounded hover:bg-gray-800 cursor-pointer">
                <input
                  type="checkbox"
                  className="form-checkbox h-4 w-4"
                  checked={value.includes(opt.value)}
                  onChange={() => toggle(opt.value)}
                />
                {opt.colorClass && <span className={`w-2 h-2 rounded-full ${opt.colorClass}`} />}
                <span className="text-sm">{opt.label}</span>
              </label>
            ))}
          </div>
          {isActive && (
            <div className="pt-2 border-t border-gray-700 flex justify-between">
              <button className="text-xs text-gray-300 hover:underline" onClick={() => onChange([])}>Bỏ chọn</button>
              <button className="text-xs text-blue-400 hover:underline" onClick={() => setOpen(false)}>Đóng</button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MultiSelect;


