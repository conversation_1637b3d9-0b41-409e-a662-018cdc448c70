import React, { useEffect, useRef, useState } from 'react';
import { useAuth } from '@/domains/auth/context/AuthContext';
import { useTranslation } from 'react-i18next';
import { Ship, Eye, EyeOff, Compass, Anchor } from 'lucide-react';
import { ApiError } from '@/shared/api/apiClient';
import LanguageToggle from '../common/LanguageToggle';

// Helper to generate random dots for buoys/lanterns
const generateDots = (count: number) => {
  return Array.from({ length: count }).map((_, i) => {
    // Random position and color for each dot
    const left = Math.random() * 100;
    const top = 60 + Math.random() * 30; // Only lower part of screen
    const size = 10 + Math.random() * 14;
    const color = [
      'bg-yellow-400', // lantern
      'bg-orange-500', // buoy
      'bg-blue-300',   // buoy
      'bg-green-400',  // buoy
      'bg-red-400',    // buoy
    ][Math.floor(Math.random() * 5)];
    const twinkle = Math.random() > 0.5;
    return (
      <div
        key={i}
        className={`absolute rounded-full shadow-lg border-2 border-white/60 ${color} ${twinkle ? 'animate-twinkle' : ''}`}
        style={{
          left: `${left}%`,
          top: `${top}%`,
          width: size,
          height: size,
          opacity: 0.85,
          zIndex: 2,
        }}
      />
    );
  });
};

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showSuccessLoading, setShowSuccessLoading] = useState(false);
  const { login } = useAuth();
  const { t } = useTranslation();
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setShowSuccessLoading(true);

    try {
      await login(username, password);
    } catch (err) {
      let message = t('auth:login.error.default');
      if (err instanceof ApiError) {
        message = err.message || message;
      } else if (err instanceof Error && err.message) {
        message = err.message;
      }
      
      setShowSuccessLoading(false);
      setError(message);
    } finally {
      setIsLoading(false);
    }
  };

  // Generate 18 dots for buoys/lanterns
  const dots = generateDots(18);

  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-b from-blue-950 via-blue-900 to-blue-800">
      {/* Language Toggle */}
      <div className="absolute top-4 right-4 z-10">
        <LanguageToggle />
      </div>
      
      {/* Ocean Night Sky with Stars */}
      <div className="absolute inset-0 z-0">
        {/* Stars */}
        {Array.from({ length: 60 }).map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-white opacity-70 animate-twinkle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 55}%`,
              width: 1 + Math.random() * 2,
              height: 1 + Math.random() * 2,
              animationDelay: `${Math.random() * 3}s`,
            }}
          />
        ))}
        {/* Moon */}
        <div className="absolute left-12 top-16 w-24 h-24 bg-gradient-to-br from-yellow-200 via-yellow-100 to-white rounded-full opacity-80 shadow-2xl" style={{ zIndex: 1 }} />
        {/* Ocean Waves (SVG) */}
        <svg className="absolute bottom-0 left-0 w-full h-48 z-10" viewBox="0 0 1440 320" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill="#0a2540" fillOpacity="0.8" d="M0,160L60,170.7C120,181,240,203,360,197.3C480,192,600,160,720,154.7C840,149,960,171,1080,186.7C1200,203,1320,213,1380,218.7L1440,224L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z" />
          <path fill="#164e7a" fillOpacity="0.7" d="M0,224L60,208C120,192,240,160,360,154.7C480,149,600,171,720,186.7C840,203,960,213,1080,197.3C1200,181,1320,139,1380,117.3L1440,96L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z" />
          <path fill="#1e6bb8" fillOpacity="0.5" d="M0,288L60,272C120,256,240,224,360,202.7C480,181,600,171,720,176C840,181,960,203,1080,213.3C1200,224,1320,224,1380,224L1440,224L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z" />
        </svg>
        {/* Buoys & Lanterns (floating dots) */}
        {dots}
        {/* Compass Rose (faint, for style) */}
        <div className="absolute top-1/2 right-10 transform -translate-y-1/2 opacity-10 z-0">
          <Compass size={200} className="text-white animate-spin" style={{ animationDuration: '20s' }} />
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-md">
          {/* Logo/Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full mb-4 border border-white/20">
              <img src="/assets/marine.png" alt="eNav logo" className="w-12 h-12 object-contain" />
            </div>
            <h1 className="text-4xl font-bold text-white mb-2">eNav</h1>
            <p className="text-blue-200">{t('auth:login.title')}</p>
          </div>

          {/* Login Form */}
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 shadow-2xl">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-white mb-2">
                  {t('auth:login.username')}
                </label>
                <div className="relative">
                  <input
                    id="username"
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value.toLowerCase())}
                    className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-200"
                    placeholder={t('auth:login.username.placeholder')}
                    required
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <Ship className="h-5 w-5 text-blue-200" />
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-white mb-2">
                  {t('auth:login.password')}
                </label>
                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-200 pr-12"
                    placeholder={t('auth:login.password.placeholder')}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-blue-200 hover:text-white transition-colors"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
              </div>

              {error && (
                <div className="bg-red-500/20 border border-red-400/30 rounded-lg p-3">
                  <p className="text-red-200 text-sm">{error}</p>
                </div>
              )}

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    {t('auth:login.loading')}
                  </div>
                ) : (
                  t('auth:login.button')
                )}
              </button>
            </form>

            {/* Demo Account Info */}
            <div className="mt-6 p-4 bg-blue-500/20 rounded-lg border border-blue-400/30">
              <p className="text-blue-200 text-sm text-center">
                <strong>{t('auth:login.demo.account')}</strong>
              </p>
            </div>
          </div>

          {/* Footer */}
          <div className="text-center mt-8">
            <p className="text-blue-200 text-sm">
              {t('auth:login.copyright')}
            </p>
          </div>
        </div>
      </div>
      {/* Custom twinkle animation for dots/stars */}
      <style>{`
        @keyframes twinkle {
          0%, 100% { opacity: 0.7; }
          50% { opacity: 1; }
        }
        .animate-twinkle {
          animation: twinkle 2.5s infinite alternate;
        }
      `}</style>
      {/* Success Loading Overlay */}
      {showSuccessLoading && (
        <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-black/80">
          {/* Animated Waves */}
          <svg className="absolute bottom-0 left-0 w-full h-64 animate-wave-move" viewBox="0 0 1440 320" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill="#0a2540" fillOpacity="0.8" d="M0,160L60,170.7C120,181,240,203,360,197.3C480,192,600,160,720,154.7C840,149,960,171,1080,186.7C1200,203,1320,213,1380,218.7L1440,224L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z" />
            <path fill="#164e7a" fillOpacity="0.7" d="M0,224L60,208C120,192,240,160,360,154.7C480,149,600,171,720,186.7C840,203,960,213,1080,197.3C1200,181,1320,139,1380,117.3L1440,96L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z" />
            <path fill="#1e6bb8" fillOpacity="0.5" d="M0,288L60,272C120,256,240,224,360,202.7C480,181,600,171,720,176C840,181,960,203,1080,213.3C1200,224,1320,224,1380,224L1440,224L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z" />
          </svg>
          {/* Glowing Anchor */}
          <div className="relative flex flex-col items-center">
            <div className="animate-anchor-glow bg-gradient-to-br from-blue-400 via-cyan-300 to-white rounded-full p-8 shadow-2xl">
              <Anchor size={64} className="text-blue-900 drop-shadow-lg" />
            </div>
            <div className="mt-8 text-2xl font-bold text-white animate-fade-in">
              Đang đăng nhập...
            </div>
          </div>
          <style>{`
            @keyframes wave-move {
              0% { transform: translateX(0); }
              100% { transform: translateX(-60px); }
            }
            .animate-wave-move {
              animation: wave-move 2s linear infinite alternate;
            }
            @keyframes anchor-glow {
              0%, 100% { box-shadow: 0 0 40px 10px #38bdf8, 0 0 80px 20px #06b6d4; }
              50% { box-shadow: 0 0 80px 30px #38bdf8, 0 0 120px 40px #06b6d4; }
            }
            .animate-anchor-glow {
              animation: anchor-glow 1.2s ease-in-out infinite alternate;
            }
            @keyframes fade-in {
              from { opacity: 0; transform: translateY(20px); }
              to { opacity: 1; transform: translateY(0); }
            }
            .animate-fade-in {
              animation: fade-in 0.8s ease-in;
            }
          `}</style>
        </div>
      )}
    </div>
  );
};

export default Login; 
