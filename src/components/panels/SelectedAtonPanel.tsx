import React from 'react';
import { X, RadioTower, Radar, Satellite } from 'lucide-react';
import { useAtonStore } from '@/domains/atons/store/useAtonStore';
import { useTheme } from '@/shared/lib/ThemeContext';
import { getAtonTypeDefinition } from '@/domains/atons/types/atonTypeDefinitions';

const DEFAULT_IMAGE = '/assets/aton-placeholder.jpg';

const safeNumber = (value?: number | null, fractionDigits = 4) => {
  if (typeof value !== 'number' || !Number.isFinite(value)) {
    return '—';
  }

  return value.toFixed(fractionDigits);
};

const formatTimestamp = (timestamp?: string | null) => {
  if (!timestamp) return '—';
  const date = new Date(timestamp);
  if (Number.isNaN(date.getTime())) return '—';
  return new Date(timestamp).toLocaleString();
};

const getStatusPresentation = (aton: { isOffPosition: boolean; isVirtual: boolean }) => {
  if (aton.isOffPosition) {
    return {
      borderClass: 'border-red-500',
      backgroundClass: 'bg-red-500 bg-opacity-10',
      badgeClass: 'bg-red-500 text-white',
      Icon: Radar,
      iconColor: 'text-red-400',
      statusLabel: 'Off position',
    };
  }

  if (aton.isVirtual) {
    return {
      borderClass: 'border-purple-500',
      backgroundClass: 'bg-purple-500 bg-opacity-10',
      badgeClass: 'bg-purple-500 text-white',
      Icon: Satellite,
      iconColor: 'text-purple-400',
      statusLabel: 'AtoN ảo',
    };
  }

  return {
    borderClass: 'border-emerald-500',
    backgroundClass: 'bg-emerald-500 bg-opacity-10',
    badgeClass: 'bg-emerald-500 text-gray-900',
    Icon: RadioTower,
    iconColor: 'text-emerald-400',
    statusLabel: 'Hoạt động',
  };
};

type SelectedAtonPanelProps = {
  className?: string;
};

export default function SelectedAtonPanel({ className }: SelectedAtonPanelProps) {
  const { theme } = useTheme();
  const selectedAton = useAtonStore((state) => state.selectedAtonId ? state.atonMap[state.selectedAtonId] : undefined);
  const clearSelectedAton = useAtonStore((state) => state.clearSelectedAton);

  if (!selectedAton) {
    return null;
  }

  const {
    id,
    name,
    mmsi,
    aidtype,
    lat,
    lon,
    channel,
    isVirtual,
    isOffPosition,
    epfd,
    dimA,
    dimB,
    dimC,
    dimD,
    timestamp,
    updatedAt,
  } = selectedAton;

  const presentation = getStatusPresentation(selectedAton);
  const typeDefinition = getAtonTypeDefinition(aidtype || undefined, isVirtual);
  const typeLabel = typeDefinition?.label || aidtype || '—';

  const renderInfoRow = (label: string, value: React.ReactNode) => (
    <div className="flex items-start justify-between gap-3 text-sm">
      <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}>{label}</span>
      <span className={`text-right font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'
        }`}>{value}</span>
    </div>
  );

  const dimensions = (dimA && dimB && dimC && dimD)
    ? `${dimA}m x ${dimB}m x ${dimC}m x ${dimD}m`
    : '—';

  return (
    <div className={`pointer-events-auto max-w-sm w-80 rounded-xl border ${theme === 'dark' ? 'border-white/10 bg-gray-900 shadow-xl shadow-black/40' : 'border-gray-200 bg-white shadow-xl shadow-gray-200/40'
      } overflow-hidden ${className ?? ''}`}>
      {/* *DO NOT REMOVE* - Temporarily commented out cover image and clear button */}
      {/* <div className="relative">
        <img
          src={DEFAULT_IMAGE}
          alt={name || 'AtoN image placeholder'}
          className="h-36 w-full object-cover"
          loading="lazy"
        />
        <button
          type="button"
          onClick={clearSelectedAton}
          className={`absolute top-3 right-3 rounded-full p-1 text-white hover:opacity-80 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 ${theme === 'dark' ? 'bg-black/60 hover:bg-black/80' : 'bg-gray-600/80 hover:bg-gray-600'
            }`}
          aria-label="Close selected AtoN panel"
        >
          <X size={16} />
        </button>
      </div> */}

      <div className="px-4 pt-3 pb-4 space-y-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className={`text-lg font-semibold truncate ${theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>{name || 'Unknown AtoN'}</h3>
            <div className={`text-sm mt-1 space-y-1 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`}>
              <p>MMSI: {mmsi || '—'}</p>
            </div>
          </div>
          <div className="flex items-center gap-2 ml-3 flex-shrink-0">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${presentation.badgeClass}`}>
              {presentation.statusLabel}
            </span>
            <button
              type="button"
              onClick={clearSelectedAton}
              className={`rounded-full p-1 hover:opacity-80 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 ${theme === 'dark' ? 'text-gray-400 hover:text-white bg-white/5 hover:bg-white/10' : 'text-gray-500 hover:text-gray-700 bg-gray-100 hover:bg-gray-200'
                }`}
              aria-label="Close selected AtoN panel"
            >
              <X size={16} />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className={`rounded-lg p-3 ${theme === 'dark' ? 'bg-white/5' : 'bg-gray-50'
            }`}>
            <p className={`text-xs uppercase tracking-wide ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
              }`}>Loại BHHH</p>
            <p className={`text-base font-semibold mt-1 ${theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>{isVirtual ? 'AtoN ảo' : 'AtoN thực'}</p>
          </div>
          <div className={`rounded-lg p-3 ${theme === 'dark' ? 'bg-white/5' : 'bg-gray-50'
            }`}>
            <p className={`text-xs uppercase tracking-wide ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
              }`}>Trạng thái</p>
            <p className={`text-base font-semibold mt-1 ${theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>{isOffPosition ? '⚠️ Off position' : '✅ On position'}</p>
          </div>
          <div className={`rounded-lg p-3 ${theme === 'dark' ? 'bg-white/5' : 'bg-gray-50'
            }`}>
            <p className={`text-xs uppercase tracking-wide ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
              }`}>Vĩ độ</p>
            <p className={`text-base font-semibold mt-1 ${theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>{safeNumber(lat, 4)}</p>
          </div>
          <div className={`rounded-lg p-3 ${theme === 'dark' ? 'bg-white/5' : 'bg-gray-50'
            }`}>
            <p className={`text-xs uppercase tracking-wide ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
              }`}>Kinh độ</p>
            <p className={`text-base font-semibold mt-1 ${theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>{safeNumber(lon, 4)}</p>
          </div>
        </div>

        <div className="space-y-2">
          {renderInfoRow('AIS Class', `Class ${channel || '—'}`)}
          {/* {renderInfoRow('EPFD', epfd || '—')} */}
          {renderInfoRow('Loại AtoN', typeLabel)}
          {renderInfoRow('Kích thước', dimensions)}
          {renderInfoRow('Cập nhật', formatTimestamp(timestamp || updatedAt))}
        </div>
      </div>
    </div>
  );
}
