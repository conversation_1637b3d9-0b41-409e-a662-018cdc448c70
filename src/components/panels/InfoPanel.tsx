import React from 'react';
import { Activity, Signal, Clock, Battery, Calendar, AlertTriangle, Zap, Sun } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { mockNavaidDevices } from '@/data/mockData';

const InfoPanel: React.FC = () => {
  const { t } = useTranslation();
  
  // L<PERSON>y thiết bị đầu tiên làm ví dụ (trong thực tế sẽ là thiết bị được chọn)
  const selectedDevice = mockNavaidDevices[0];

  return (
    <div className="p-4 text-sm">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">{t('device:device.details')}</h2>
        <div className="text-right">
          <div className="text-2xl font-bold">{selectedDevice.name}</div>
          <div className="text-gray-400">{selectedDevice.locationVi}</div>
        </div>
      </div>
      
      <div className="mb-6">
        <div className={`text-xl font-bold ${
          selectedDevice.status === 'operational' ? 'text-green-500' :
          selectedDevice.status === 'maintenance' ? 'text-yellow-500' : 'text-red-500'
        }`}>
          {t(selectedDevice.status)}
        </div>
        <div className="text-gray-400">{t('common:last.updated')}: {selectedDevice.lastUpdateTime}</div>
      </div>
      
      <div className="space-y-6">
        <div className="bg-gray-800 rounded-lg p-4">
          <h3 className="font-semibold mb-2 flex items-center">
            <Activity size={16} className="mr-2 text-blue-500" />
            {t('device:device.parameters')}
          </h3>
          <div className="space-y-3">
            <div>
              <div className="flex justify-between mb-1">
                <span>{t('device:battery.voltage')}</span>
                <span className={`${selectedDevice.batteryVoltage > 3.8 ? 'text-green-500' : 'text-yellow-500'}`}>
                  {selectedDevice.batteryVoltage}V
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${selectedDevice.batteryVoltage > 3.8 ? 'bg-green-500' : 'bg-yellow-500'}`}
                  style={{ width: `${(selectedDevice.batteryVoltage / 4.2) * 100}%` }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span>{t('device:solar.charge')}</span>
                <span className="text-blue-500">{selectedDevice.solarCharge} mA</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full" 
                  style={{ width: `${Math.min((selectedDevice.solarCharge / 0.1) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span>{t('device:load.amps')}</span>
                <span className="text-purple-500">{selectedDevice.loadAmps} A</span>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span>{t('device:light.status')}</span>
                <span className={`${selectedDevice.lightStatus === 'ON' ? 'text-green-500' : 'text-red-500'}`}>
                  {selectedDevice.lightStatus}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <h3 className="font-semibold mb-2 flex items-center">
            <Signal size={16} className="mr-2 text-blue-500" />
            {t('device:device.information')}
          </h3>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <div className="text-gray-400">{t('device:serial.number')}</div>
              <div className="text-xs">{selectedDevice.serialNumber}</div>
            </div>
            <div>
              <div className="text-gray-400">{t('device:flash.code')}</div>
              <div>{selectedDevice.flashCode}</div>
            </div>
            <div>
              <div className="text-gray-400">{t('device:position.status')}</div>
              <div className={`${selectedDevice.positionStatus === 'OK' ? 'text-green-500' : 'text-red-500'}`}>
                {selectedDevice.positionStatus}
              </div>
            </div>
            <div>
              <div className="text-gray-400">{t('device:signal.strength')}</div>
              <div>{selectedDevice.signalStrength}%</div>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <h3 className="font-semibold mb-2 flex items-center">
            <Clock size={16} className="mr-2 text-blue-500" />
            {t('device:iala.information')}
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <div className="text-gray-400">{t('device:iala.name')}</div>
              <div className="text-right text-sm">{selectedDevice.ialaName}</div>
            </div>
            <div className="flex justify-between">
              <div className="text-gray-400">{t('common:coordinates')}</div>
              <div className="text-right text-xs">
                {selectedDevice.coordinates.lat}°N, {selectedDevice.coordinates.lng}°E
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <h3 className="font-semibold mb-2 flex items-center">
            <Calendar size={16} className="mr-2 text-blue-500" />
            {t('device:maintenance.schedule')}
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <div className="text-gray-400">{t('device:last.maintenance')}</div>
              <div>{selectedDevice.lastMaintenance}</div>
            </div>
            <div className="flex justify-between">
              <div className="text-gray-400">{t('device:next.scheduled')}</div>
              <div>{selectedDevice.nextMaintenance}</div>
            </div>
            <div className="flex justify-between">
              <div className="text-gray-400">{t('device:uptime')}</div>
              <div>{selectedDevice.uptime}%</div>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <h3 className="font-semibold mb-2 flex items-center">
            <AlertTriangle size={16} className="mr-2 text-yellow-500" />
            {t('device:recent.activity')}
          </h3>
          <div className="space-y-2">
            <div className="flex items-start space-x-2">
              <div className="text-gray-400 whitespace-nowrap text-xs">05/07 18:06</div>
              <div className="text-sm">Cập nhật trạng thái thiết bị - Điện áp pin: {selectedDevice.batteryVoltage}V</div>
            </div>
            <div className="flex items-start space-x-2">
              <div className="text-gray-400 whitespace-nowrap text-xs">05/07 17:45</div>
              <div className="text-sm">Kiểm tra tín hiệu GPS - Vị trí xác nhận</div>
            </div>
            <div className="flex items-start space-x-2">
              <div className="text-gray-400 whitespace-nowrap text-xs">05/07 16:30</div>
              <div className="text-sm">Đồng bộ dữ liệu với hệ thống trung tâm</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InfoPanel;