import React, { useState } from 'react';
import { X } from 'lucide-react';
import { useVesselStore } from '@/domains/vessels/store/useVesselStore';
import { formatCog, formatHeading, formatSog } from '@/shared/utils/formatters';
import { useTheme } from '@/shared/lib/ThemeContext';
import PlaybackDateModal from '../map/PlaybackDateModal';

const DEFAULT_IMAGE = '/assets/vessel-placeholder.webp';

const safeNumber = (value?: number, fractionDigits = 2) => {
  if (typeof value !== 'number' || !Number.isFinite(value)) {
    return '—';
  }

  return value.toFixed(fractionDigits);
};

type SelectedVesselPanelProps = {
  className?: string;
};

export default function SelectedVesselPanel({ className }: SelectedVesselPanelProps) {
  const { theme } = useTheme();
  const selectedVessel = useVesselStore((state) => state.selectedVesselId ? state.vesselMap[state.selectedVesselId] : undefined);
  const clearSelectedVessel = useVesselStore((state) => state.clearSelectedVessel);
  const startPlayback = useVesselStore((state) => state.startPlayback);
  const isLoadingTrack = useVesselStore((state) => state.isLoadingTrack);
  const [isPlaybackModalOpen, setIsPlaybackModalOpen] = useState(false);

  if (!selectedVessel) {
    return null;
  }

  const {
    name,
    mmsi,
    callsign,
    cog,
    sog,
    hdg,
    lat,
    lon,
    navStatus,
    vesselTypeLabel,
    timestamp,
    length,
    width,
    vesselClass,
    flagIcon,
    flagLabel,
  } = selectedVessel;

  const renderInfoRow = (label: string, value: React.ReactNode) => (
    <div className="flex items-start justify-between gap-3 text-sm">
      <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}>{label}</span>
      <span className={`text-right font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'
        }`}>{value}</span>
    </div>
  );

  const statusLabel = navStatus ? navStatus.replace(/_/g, ' ') : '—';
  const vesselClassLabel = vesselClass ? `Class ${vesselClass}` : '-';
  const iso2 = (flagIcon && flagIcon !== 'DEFAULT' ? String(flagIcon) : 'UN').toLowerCase();
  const vesselDimensions = (length && width) ? `${length}m x ${width}m` : '—';

  return (
    <div className={`pointer-events-auto max-w-sm w-80 rounded-xl border ${theme === 'dark' ? 'border-white/10 bg-gray-900 shadow-xl shadow-black/40' : 'border-gray-200 bg-white shadow-xl shadow-gray-200/40'
      } overflow-hidden ${className ?? ''}`}>
      {/* *DO NOT REMOVE* - Temporarily commented out cover image and clear button */}
      {/* <div className="relative">
        <img
          src={DEFAULT_IMAGE}
          alt={name || 'Vessel image placeholder'}
          className="h-36 w-full object-cover"
          loading="lazy"
        />
        <button
          type="button"
          onClick={clearSelectedVessel}
          className={`absolute top-3 right-3 rounded-full p-1 text-white hover:opacity-80 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 ${theme === 'dark' ? 'bg-black/60 hover:bg-black/80' : 'bg-gray-600/80 hover:bg-gray-600'
            }`}
          aria-label="Close selected vessel panel"
        >
          <X size={16} />
        </button>
      </div> */}

      <div className="px-4 pt-3 pb-4 space-y-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span
                title={flagLabel || 'Unknown'}
                aria-label={flagLabel || 'Unknown'}
                className={`fi fi-${iso2} flex-shrink-0 ${theme === 'dark' ? 'border-white/20' : 'border-gray-300'}`}
                style={{ width: '2rem', height: '1.25rem' }}
              />
              <h3 className={`text-lg font-semibold truncate ${theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>{name || 'Unknown vessel'}</h3>
            </div>
            <div className={`text-sm mt-1 space-y-1 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`}>
              <p>MMSI: {mmsi || '—'} | Hô hiệu: {callsign || '—'}</p>
            </div>
          </div>
          <button
            type="button"
            onClick={clearSelectedVessel}
            className={`rounded-full p-1 hover:opacity-80 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 flex-shrink-0 ${theme === 'dark' ? 'text-gray-400 hover:text-white bg-white/5 hover:bg-white/10' : 'text-gray-500 hover:text-gray-700 bg-gray-100 hover:bg-gray-200'
              }`}
            aria-label="Close selected vessel panel"
          >
            <X size={16} />
          </button>
        </div>

        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className={`rounded-lg p-3 ${theme === 'dark' ? 'bg-white/5' : 'bg-gray-50'
            }`}>
            <p className={`text-xs uppercase tracking-wide ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
              }`}>Tốc độ</p>
            <p className={`text-base font-semibold mt-1 ${theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>{formatSog(sog)}</p>
          </div>
          <div className={`rounded-lg p-3 ${theme === 'dark' ? 'bg-white/5' : 'bg-gray-50'
            }`}>
            <p className={`text-xs uppercase tracking-wide ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
              }`}>Trạng thái</p>
            <p className={`text-base font-semibold mt-1 capitalize ${theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>{statusLabel}</p>
          </div>
          <div className={`rounded-lg p-3 ${theme === 'dark' ? 'bg-white/5' : 'bg-gray-50'
            }`}>
            <p className={`text-xs uppercase tracking-wide ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
              }`}>Hướng tàu chạy</p>
            <p className={`text-base font-semibold mt-1 ${theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>{formatCog(cog)}</p>
          </div>
          <div className={`rounded-lg p-3 ${theme === 'dark' ? 'bg-white/5' : 'bg-gray-50'
            }`}>
            <p className={`text-xs uppercase tracking-wide ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
              }`}>Hướng mũi tàu</p>
            <p className={`text-base font-semibold mt-1 ${theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>{formatHeading(hdg)}</p>
          </div>
        </div>

        <div className="space-y-2">
          {renderInfoRow('AIS Class', vesselClassLabel)}
          {renderInfoRow('Kích thước tàu', vesselDimensions)}
          {renderInfoRow('Vĩ độ', safeNumber(lat, 4))}
          {renderInfoRow('Kinh độ', safeNumber(lon, 4))}
          {renderInfoRow('Loại tàu', vesselTypeLabel || '—')}
          {renderInfoRow('Cập nhật', timestamp ? new Date(timestamp).toLocaleString() : '—')}
        </div>

        <button
          type="button"
          onClick={() => setIsPlaybackModalOpen(true)}
          disabled={isLoadingTrack}
          className={`w-full rounded-lg font-semibold py-2 transition-colors ${isLoadingTrack
            ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
            : 'bg-blue-500 hover:bg-blue-400 text-white'
            }`}
        >
          {isLoadingTrack ? 'Đang tải...' : 'Xem hành trình'}
        </button>
      </div>

      <PlaybackDateModal
        isOpen={isPlaybackModalOpen}
        onClose={() => setIsPlaybackModalOpen(false)}
        onConfirm={async (fromDate, toDate) => {
          try {
            await startPlayback(mmsi, fromDate, toDate);
            setIsPlaybackModalOpen(false);
            // Automatically start playing after data is loaded
            // The store will handle this in the startPlayback function
          } catch (error) {
            console.error('Failed to start playback:', error);
            // Error is already handled in the store
          }
        }}
        vesselName={name || 'Unknown Vessel'}
      />
    </div>
  );
}

