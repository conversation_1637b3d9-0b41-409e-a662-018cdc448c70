import { useMemo } from 'react';
import { ChevronLeft, ChevronRight, Ship, AlertCircle, BadgeInfoIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import type { ConnectionStatus } from '@/domains/vessels/types/vessel';
import type { AtonInfo } from '@/domains/atons/types/aton';
import type { VesselInfo } from '@/domains/vessels/types/vessel';
import VesselInfoPanel from '../VesselInfoPanel';
import InfoPanel from '../InfoPanel';
import AtonInfoPanel from '../AtonInfoPanel';
import AlertsList from '@/components/ui/AlertsList';

export type SidePanelKey = 'vessels' | 'info' | 'atons' | 'alerts';

interface RightPanelProps {
  theme: 'dark' | 'light';
  sidePanel: SidePanelKey;
  onSidePanelChange: (panel: SidePanelKey) => void;
  isCollapsed: boolean;
  onToggleCollapse: (collapsed: boolean) => void;
  vessels: VesselInfo[];
  connectionStatus: ConnectionStatus;
  atons: AtonInfo[];
  onSelectVessel: (coords: [number, number]) => void;
}

type ThemeClasses = {
  panelBg: string;
  border: string;
  hover: string;
  activeButton: string;
  inactiveButton: string;
  toggle: string;
};

export function RightPanel({
  theme,
  sidePanel,
  onSidePanelChange,
  isCollapsed,
  onToggleCollapse,
  vessels,
  connectionStatus,
  atons,
  onSelectVessel,
}: RightPanelProps) {
  const { t } = useTranslation();
  const themeClasses = useMemo<ThemeClasses>(() => ({
    panelBg: theme === 'dark' ? 'bg-gray-900' : 'bg-white',
    border: theme === 'dark' ? 'border-gray-700' : 'border-gray-200',
    hover: theme === 'dark' ? 'hover:bg-gray-800 text-gray-400' : 'hover:bg-gray-100 text-gray-400',
    activeButton: theme === 'dark' ? 'bg-gray-800 text-white' : 'bg-gray-100 text-gray-900',
    inactiveButton: theme === 'dark' ? 'hover:bg-gray-800 text-gray-400' : 'hover:bg-gray-100 text-gray-600',
    toggle: theme === 'dark' ? 'hover:bg-gray-800' : 'hover:bg-gray-100',
  }), [theme]);

  if (isCollapsed) {
    return (
      <div className={`w-20 border-l ${themeClasses.panelBg} ${themeClasses.border} flex flex-col h-full transition-all duration-300 ease-in-out`}>
        <div className={`p-4 border-b flex items-center justify-center ${themeClasses.border}`}>
          <button
            onClick={() => onToggleCollapse(false)}
            className={`p-2 rounded-md transition ${themeClasses.toggle}`}
            title={t('dashboard:expandpanel', 'Expand panel')}
          >
            <ChevronLeft size={18} className="text-gray-400" />
          </button>
        </div>
        <div className="flex-1 flex flex-col items-center space-y-2 p-2">
          <CollapsedButton
            active={sidePanel === 'vessels'}
            onClick={() => onSidePanelChange('vessels')}
            title={t('dashboard:vessels')}
            themeClasses={themeClasses}
          >
            <Ship size={20} />
          </CollapsedButton>
          {/* <CollapsedButton
            active={sidePanel === 'info'}
            onClick={() => onSidePanelChange('info')}
            title={t('dashboard:devices')}
            themeClasses={themeClasses}
          >
            <BadgeInfoIcon size={20} />
          </CollapsedButton> */}
          <CollapsedButton
            active={sidePanel === 'alerts'}
            onClick={() => onSidePanelChange('alerts')}
            title={t('dashboard:alerts')}
            themeClasses={themeClasses}
          >
            <AlertCircle size={20} />
          </CollapsedButton>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-96 border-l flex flex-col h-full transition-all duration-300 ease-in-out ${themeClasses.panelBg} ${themeClasses.border}`}>
      <div className={`p-4 border-b flex items-center justify-between ${themeClasses.border}`}>
        <div className="flex space-x-1">
          <PanelButton
            label={t('dashboard:vessels')}
            isActive={sidePanel === 'vessels'}
            onClick={() => onSidePanelChange('vessels')}
            activeClass={themeClasses.activeButton}
            inactiveClass={themeClasses.inactiveButton}
          />
          {/* <PanelButton
            label={t('dashboard:devices')}
            isActive={sidePanel === 'info'}
            onClick={() => onSidePanelChange('info')}
            activeClass={themeClasses.activeButton}
            inactiveClass={themeClasses.inactiveButton}
          /> */}
          <PanelButton
            label={t('dashboard:atons')}
            isActive={sidePanel === 'atons'}
            onClick={() => onSidePanelChange('atons')}
            activeClass={themeClasses.activeButton}
            inactiveClass={themeClasses.inactiveButton}
          />
          <PanelButton
            label={t('dashboard:alerts')}
            isActive={sidePanel === 'alerts'}
            onClick={() => onSidePanelChange('alerts')}
            activeClass={themeClasses.activeButton}
            inactiveClass={themeClasses.inactiveButton}
          />
        </div>
        <button
          onClick={() => onToggleCollapse(true)}
          className={`p-2 rounded-md transition ${themeClasses.toggle}`}
          title={t('dashboard:collapsepanel', 'Collapse panel')}
        >
          <ChevronRight size={18} className="text-gray-400" />
        </button>
      </div>

      <div className="flex-1 overflow-y-auto">
        {sidePanel === 'vessels' && (
          <VesselInfoPanel
            vessels={vessels}
            connectionStatus={connectionStatus}
            onSelectVessel={onSelectVessel}
          />
        )}
        {sidePanel === 'info' && <InfoPanel />}
        {sidePanel === 'atons' && (
          <AtonInfoPanel atons={atons} onSelectAton={onSelectVessel} />
        )}
        {sidePanel === 'alerts' && <AlertsList />}
      </div>
    </div>
  );
}

interface CollapsedButtonProps {
  active: boolean;
  onClick: () => void;
  title: string;
  themeClasses: ThemeClasses;
  children: React.ReactNode;
}

function CollapsedButton({ active, onClick, title, themeClasses, children }: CollapsedButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`w-10 h-10 rounded-md flex items-center justify-center transition ${
        active ? 'bg-orange-500 text-white' : themeClasses.hover
      }`}
      title={title}
    >
      {children}
    </button>
  );
}

interface PanelButtonProps {
  label: string;
  isActive: boolean;
  onClick: () => void;
  activeClass: string;
  inactiveClass: string;
}

function PanelButton({ label, isActive, onClick, activeClass, inactiveClass }: PanelButtonProps) {
  return (
    <button
      className={`px-3 py-2 text-sm rounded-md transition ${isActive ? activeClass : inactiveClass}`}
      onClick={onClick}
    >
      {label}
    </button>
  );
}

export default RightPanel;
