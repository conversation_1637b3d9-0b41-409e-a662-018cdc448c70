import React, { useEffect, useMemo, useState } from 'react';
import { RadioTower, Radar, Satellite } from 'lucide-react';
import type { AtonInfo } from '@/domains/atons/types/aton';
import { useTheme } from '@/shared/lib/ThemeContext';
import { useAtonStore } from '@/domains/atons/store/useAtonStore';
import { getAtonTypeDefinition } from '@/domains/atons/types/atonTypeDefinitions';

const PAGE_SIZE = 100;

interface AtonInfoPanelProps {
  atons: AtonInfo[];
  onSelectAton?: (lngLat: [number, number]) => void;
}

const formatTimestamp = (timestamp?: string | null) => {
  if (!timestamp) return '—';
  const date = new Date(timestamp);
  if (Number.isNaN(date.getTime())) return '—';
  return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
};

const getStatusPresentation = (aton: AtonInfo) => {
  if (aton.isOffPosition) {
    return {
      borderClass: 'border-red-500',
      backgroundClass: 'bg-red-500 bg-opacity-10',
      badgeClass: 'bg-red-500 text-white',
      Icon: Radar,
      iconColor: 'text-red-400',
      statusLabel: 'Off position',
    };
  }

  // if (aton.isVirtual) {
  //   return {
  //     borderClass: 'border-purple-500',
  //     backgroundClass: 'bg-purple-500 bg-opacity-10',
  //     badgeClass: 'bg-purple-500 text-white',
  //     Icon: Satellite,
  //     iconColor: 'text-purple-400',
  //     statusLabel: 'AtoN ảo',
  //   };
  // }

  return {
    borderClass: 'border-emerald-500',
    backgroundClass: 'bg-emerald-500 bg-opacity-10',
    badgeClass: 'bg-emerald-500 text-gray-900',
    Icon: RadioTower,
    iconColor: 'text-emerald-400',
    statusLabel: 'Hoạt động',
  };
};

export default function AtonInfoPanel({ atons, onSelectAton }: AtonInfoPanelProps) {
  const { theme } = useTheme();
  const [page, setPage] = useState(1);

  const totalPages = Math.max(1, Math.ceil(atons.length / PAGE_SIZE));

  useEffect(() => {
    setPage((prev) => Math.min(prev, totalPages));
  }, [totalPages]);

  const currentPage = Math.max(1, Math.min(page, totalPages));
  const startIndex = (currentPage - 1) * PAGE_SIZE;
  const endIndex = Math.min(startIndex + PAGE_SIZE, atons.length);

  const visibleAtons = useMemo(
    () => atons.slice(startIndex, endIndex),
    [atons, startIndex, endIndex]
  );

  const handlePrev = () => setPage((prev) => Math.max(prev - 1, 1));
  const handleNext = () => setPage((prev) => Math.min(prev + 1, totalPages));

  return (
    <div className="p-4">
      <h2 className={`text-xl font-bold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
        Danh sách AtoN
      </h2>

      <div className={`mb-4 text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
        {atons.length} AtoN
      </div>

      <div className="space-y-4">
        {atons.length === 0 ? (
          <div className={`text-sm italic ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Chưa có AtoN nào
          </div>
        ) : (
          visibleAtons.map((aton) => (
            <AtonCard key={aton.id} aton={aton} onJump={onSelectAton} theme={theme} />
          ))
        )}
      </div>

      {atons.length > 0 && (
        <div
          className={`flex flex-col sm:flex-row sm:items-center sm:justify-between mt-4 text-xs gap-3 ${
            theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
          }`}
        >
          <span>
            Hiển thị {startIndex + 1}-{endIndex} trên tổng số {atons.length} AtoN
          </span>
          <div className="flex items-center gap-2">
            <button
              type="button"
              onClick={handlePrev}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded-md border transition ${
                theme === 'dark'
                  ? `border-gray-700 ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-800'}`
                  : `border-gray-300 ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}`
              }`}
            >
              Trước
            </button>
            <span>
              Trang {currentPage}/{totalPages}
            </span>
            <button
              type="button"
              onClick={handleNext}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded-md border transition ${
                theme === 'dark'
                  ? `border-gray-700 ${
                      currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-800'
                    }`
                  : `border-gray-300 ${
                      currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
                    }`
              }`}
            >
              Sau
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

const AtonCard: React.FC<{
  aton: AtonInfo;
  onJump?: (lngLat: [number, number]) => void;
  theme: 'light' | 'dark';
}> = ({ aton, onJump, theme }) => {
  const presentation = getStatusPresentation(aton);
  const { Icon } = presentation;
  const setSelectedAton = useAtonStore((state) => state.setSelectedAton);

  const handleClick = () => {
    setSelectedAton(aton.id);
    if (onJump && Number.isFinite(aton.lon) && Number.isFinite(aton.lat)) {
      onJump([aton.lon, aton.lat]);
    }
  };

  return (
    <button
      type="button"
      onClick={handleClick}
      className={`w-full text-left p-4 rounded-xl border-l-4 transition-colors ${presentation.borderClass} ${presentation.backgroundClass} hover:bg-opacity-20 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-emerald-400 ${
        theme === 'dark' ? 'ring-offset-gray-900' : 'ring-offset-white'
      }`}
    >
      <div className="flex items-start">
        <div className="mr-3 mt-1">
          <Icon size={18} className={presentation.iconColor} />
        </div>
        <div className="flex-1">
          <div className="flex justify-between items-start">
            <div>
              <h3 className={`font-semibold text-base ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                {aton.name || 'Unknown'}
              </h3>
              <div className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                MMSI: {aton.mmsi}
              </div>
            </div>
            <div className={`text-xs whitespace-nowrap ml-3 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
              {formatTimestamp(aton.timestamp ?? aton.updatedAt)}
            </div>
          </div>

          <div className="mt-3 flex flex-wrap items-center gap-2 text-xs">
            <span className={`px-2 py-1 rounded-full font-medium ${presentation.badgeClass}`}>
              {presentation.statusLabel}
            </span>
            {aton.channel && (
              <span className={theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}>
                AIS Class: {aton.channel}
              </span>
            )}
            {aton.aidtype && (() => {
              const typeDefinition = getAtonTypeDefinition(aton.aidtype, aton.isVirtual);
              const typeLabel = typeDefinition?.label || aton.aidtype || '—';
              return (
                <span className={theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}>
                  Loại: {typeLabel}
                </span>
              );
            })()}
          </div>

          <div className={`mt-2 grid grid-cols-2 gap-x-4 gap-y-1 text-xs ${
            theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
          }`}>
            <span>Vĩ độ: {Number.isFinite(aton.lat) ? aton.lat.toFixed(4) : '—'}</span>
            <span>Kinh độ: {Number.isFinite(aton.lon) ? aton.lon.toFixed(4) : '—'}</span>
            <span className="col-span-2">
              Tọa độ cập nhật: {formatTimestamp(aton.updatedAt)}
            </span>
          </div>
        </div>
      </div>
    </button>
  );
};

