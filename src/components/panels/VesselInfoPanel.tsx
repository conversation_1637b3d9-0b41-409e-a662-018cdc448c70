import React, { useEffect, useMemo, useState } from 'react';
import { Anchor, Navigation, AlertTriangle } from 'lucide-react';
import { VesselInfo, ConnectionStatus } from '@/domains/vessels/types/vessel';
import { formatHeading, formatSog, formatCog } from '@/shared/utils/formatters';
import { useVesselStore } from '@/domains/vessels/store/useVesselStore';
import { useTheme } from '@/shared/lib/ThemeContext';

const PAGE_SIZE = 100;

interface VesselInfoPanelProps {
  vessels: VesselInfo[];
  connectionStatus: ConnectionStatus;
  onSelectVessel?: (lngLat: [number, number]) => void;
}

const formatTimestamp = (timestamp?: string) => {
  if (!timestamp) return '—';
  const date = new Date(timestamp);
  if (Number.isNaN(date.getTime())) return '—';
  return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
};

const getSpeedPresentation = (sog?: number) => {
  const safeSpeed = typeof sog === 'number' && Number.isFinite(sog) ? Math.max(sog, 0) : 0;

  if (safeSpeed === 0) {
    return {
      borderClass: 'border-blue-500',
      backgroundClass: 'bg-blue-500 bg-opacity-10',
      badgeClass: 'bg-blue-600 text-white',
      Icon: Anchor,
      iconColor: 'text-blue-400',
      statusLabel: 'Neo đậu'
    };
  }

  if (safeSpeed <= 10) {
    return {
      borderClass: 'border-yellow-500',
      backgroundClass: 'bg-yellow-500 bg-opacity-10',
      badgeClass: 'bg-yellow-500 text-gray-900',
      Icon: Navigation,
      iconColor: 'text-yellow-400',
      statusLabel: 'Đang di chuyển'
    };
  }

  return {
    borderClass: 'border-red-500',
    backgroundClass: 'bg-red-500 bg-opacity-10',
    badgeClass: 'bg-red-500 text-white',
    Icon: AlertTriangle,
    iconColor: 'text-red-400',
    statusLabel: 'Di chuyển nhanh'
  };
};

const formatNavStatus = (status?: string) => {
  if (!status) return undefined;
  return status.replace(/_/g, ' ');
};

export default function VesselInfoPanel({ vessels, connectionStatus, onSelectVessel }: VesselInfoPanelProps) {
  const { theme } = useTheme();
  const [page, setPage] = useState(1);

  const totalPages = Math.max(1, Math.ceil(vessels.length / PAGE_SIZE));

  useEffect(() => {
    setPage((prev) => Math.min(prev, totalPages));
  }, [totalPages]);

  const currentPage = Math.max(1, Math.min(page, totalPages));
  const startIndex = (currentPage - 1) * PAGE_SIZE;
  const endIndex = Math.min(startIndex + PAGE_SIZE, vessels.length);

  const visibleVessels = useMemo(
    () => vessels.slice(startIndex, endIndex),
    [vessels, startIndex, endIndex]
  );

  const handlePrev = () => {
    setPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNext = () => {
    setPage((prev) => Math.min(prev + 1, totalPages));
  };

  return (
    <div className="p-4">
      <h2 className={`text-xl font-bold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>Thông tin tàu</h2>

      <div className="flex items-center mb-4 text-sm">
        <span
          className={`inline-block w-3 h-3 rounded-full mr-2 ${connectionStatus.connected ? 'bg-green-500' : 'bg-red-500'
            }`}
        />
        <span className={`mr-2 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>{connectionStatus.message}</span>
        <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}>{vessels.length} tàu</span>
      </div>

      <div className="space-y-4">
        {vessels.length === 0 ? (
          <div className={`text-sm italic ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>Chưa có tàu nào</div>
        ) : (
          visibleVessels.map((vessel) => (
            <VesselCard key={vessel.mmsi} vessel={vessel} onJump={onSelectVessel} theme={theme} />
          ))
        )}
      </div>

      {vessels.length > 0 && (
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between mt-4 text-xs gap-3 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
          <span>
            Hiển thị {startIndex + 1}-{endIndex} trên tổng số {vessels.length} tàu
          </span>
          <div className="flex items-center gap-2">
            <button
              type="button"
              onClick={handlePrev}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded-md border transition ${theme === 'dark' 
                ? `border-gray-700 ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-800'}` 
                : `border-gray-300 ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}`}`}
            >
              Trước
            </button>
            <span>
              Trang {currentPage}/{totalPages}
            </span>
            <button
              type="button"
              onClick={handleNext}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded-md border transition ${theme === 'dark' 
                ? `border-gray-700 ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-800'}` 
                : `border-gray-300 ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}`}`}
            >
              Sau
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

const VesselCard: React.FC<{ vessel: VesselInfo; onJump?: (lngLat: [number, number]) => void; theme: 'light' | 'dark' }> = ({ vessel, onJump, theme }) => {
  const presentation = getSpeedPresentation(vessel.sog);
  const { Icon } = presentation;
  const setSelectedVessel = useVesselStore((state) => state.setSelectedVessel);

  const handleClick = () => {
    // Select the vessel (same behavior as clicking on map icon)
    setSelectedVessel(vessel.mmsi);
    
    // Also center the map on the vessel if onJump is provided
    if (onJump && Number.isFinite(vessel.lon) && Number.isFinite(vessel.lat)) {
      onJump([vessel.lon, vessel.lat]);
    }
  };

  return (
    <button
      type="button"
      onClick={handleClick}
      className={`w-full text-left p-4 rounded-xl border-l-4 transition-colors ${presentation.borderClass} ${presentation.backgroundClass} hover:bg-opacity-20 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-400 ${theme === 'dark' ? 'ring-offset-gray-900' : 'ring-offset-white'}`}
    >
      <div className="flex items-start">
        <div className="mr-3 mt-1">
          <Icon size={18} className={presentation.iconColor} />
        </div>
        <div className="flex-1">
          <div className="flex justify-between items-start">
            <div>
              <h3 className={`font-semibold text-base ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>{vessel.name || 'Unknown'}</h3>
              <div className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>MMSI: {vessel.mmsi}</div>
            </div>
            <div className={`text-xs whitespace-nowrap ml-3 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
              {formatTimestamp(vessel.timestamp)}
            </div>
          </div>

          <div className="mt-3 flex flex-wrap items-center gap-2 text-xs">
            <span className={`px-2 py-1 rounded-full font-medium ${presentation.badgeClass}`}>
              {presentation.statusLabel}
            </span>
            <span className={theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}>Tốc độ: {formatSog(vessel.sog)}</span>
            <span className={theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}>Hướng tàu chạy: {formatCog(vessel.cog)}</span>
            <span className={theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}>Hướng mũi tàu: {formatHeading(vessel.hdg)}</span>
          </div>

          <div className={`mt-2 grid grid-cols-2 gap-x-4 gap-y-1 text-xs ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
            <span>Vĩ độ: {Number.isFinite(vessel.lat) ? vessel.lat.toFixed(4) : '—'}</span>
            <span>Kinh độ: {Number.isFinite(vessel.lon) ? vessel.lon.toFixed(4) : '—'}</span>
            {formatNavStatus(vessel.navStatus) && (
              <span className="col-span-2">Trạng thái: {formatNavStatus(vessel.navStatus)}</span>
            )}
          </div>
        </div>
      </div>
    </button>
  );
};
