import { useCallback, useEffect, useRef, useState } from 'react';
import maplibregl from 'maplibre-gl';
import type { SidePanelKey } from '../components/dashboard/RightPanel';

export interface DashboardControls {
  selectedDate: string;
  isFullscreen: boolean;
  toggleFullscreen: () => void;
  isFocusMode: boolean;
  toggleFocusMode: () => void;
  isSidebarCollapsed: boolean;
  toggleSidebarCollapsed: () => void;
  isRightPanelCollapsed: boolean;
  setRightPanelCollapsed: (collapsed: boolean) => void;
  sidePanel: SidePanelKey;
  setSidePanel: (panel: SidePanelKey) => void;
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  appContainerRef: React.MutableRefObject<HTMLDivElement | null>;
  handleMapReady: (instance: maplibregl.Map | null) => void;
  jumpToCoordinates: (coords: [number, number]) => void;
}

export function useDashboardControls(): DashboardControls {
  const [selectedDate] = useState<string>(() => new Date().toLocaleDateString('en-GB'));
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isFocusMode, setIsFocusMode] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isRightPanelCollapsed, setIsRightPanelCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sidePanel, setSidePanel] = useState<SidePanelKey>('vessels');

  const appContainerRef = useRef<HTMLDivElement | null>(null);
  const mapRef = useRef<maplibregl.Map | null>(null);
  const pendingJumpRef = useRef<[number, number] | null>(null);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(Boolean(document.fullscreenElement));
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      appContainerRef.current?.requestFullscreen().catch(() => {
        setIsFullscreen(false);
      });
    } else {
      document.exitFullscreen().catch(() => {
        setIsFullscreen(true);
      });
    }
  }, []);

  const toggleFocusMode = useCallback(() => {
    setIsFocusMode((prev) => !prev);
  }, []);

  const toggleSidebarCollapsed = useCallback(() => {
    setIsSidebarCollapsed((prev) => !prev);
  }, []);

  const handleMapReady = useCallback((instance: maplibregl.Map | null) => {
    mapRef.current = instance;
    if (instance && pendingJumpRef.current) {
      instance.jumpTo({ center: pendingJumpRef.current });
      pendingJumpRef.current = null;
    }
  }, []);

  const jumpToCoordinates = useCallback((coords: [number, number]) => {
    const map = mapRef.current;
    if (map) {
      map.jumpTo({ center: coords });
    } else {
      pendingJumpRef.current = coords;
    }
  }, []);

  return {
    selectedDate,
    isFullscreen,
    toggleFullscreen,
    isFocusMode,
    toggleFocusMode,
    isSidebarCollapsed,
    toggleSidebarCollapsed,
    isRightPanelCollapsed,
    setRightPanelCollapsed: setIsRightPanelCollapsed,
    sidePanel,
    setSidePanel,
    searchQuery,
    setSearchQuery,
    appContainerRef,
    handleMapReady,
    jumpToCoordinates,
  };
}
