import { type RefObject, useEffect, useRef, useState } from 'react';
import maplibregl from 'maplibre-gl';

type MapOptions = Omit<maplibregl.MapOptions, 'container'>;

type ControlFactory = () => maplibregl.IControl;

export interface MapControlSpec {
  factory: ControlFactory;
  position?: maplibregl.ControlPosition;
}

export interface UseMapInstanceOptions {
  containerRef: RefObject<HTMLDivElement>;
  mapOptions: MapOptions;
  controls?: MapControlSpec[];
  onLoad?: (map: maplibregl.Map) => void;
  onError?: (error: Error) => void;
}

interface UseMapInstanceResult {
  map: maplibregl.Map | null;
  isLoaded: boolean;
}

/**
 * Shared hook for creating and managing a MapLibre map instance.
 * Handles control registration, load state, and teardown to keep MapView lean.
 */
export function useMapInstance({
  containerRef,
  mapOptions,
  controls = [],
  onLoad,
  onError,
}: UseMapInstanceOptions): UseMapInstanceResult {
  const mapRef = useRef<maplibregl.Map | null>(null);
  const [map, setMap] = useState<maplibregl.Map | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (!containerRef.current || mapRef.current) {
      return;
    }

    let disposed = false;

    const initializeMap = () => {
      try {
        const instance = new maplibregl.Map({
          container: containerRef.current as HTMLDivElement,
          ...mapOptions,
        });

        mapRef.current = instance;
        setMap(instance);

        const controlInstances = controls.map((spec) => {
          const control = spec.factory();
          instance.addControl(control, spec.position);
          return control;
        });

        const handleLoad = () => {
          setIsLoaded(true);
          onLoad?.(instance);
        };

        const handleError = (event: maplibregl.ErrorEvent) => {
          const error = event?.error instanceof Error
            ? event.error
            : new Error('MapLibre emitted an unknown error event');
          onError?.(error);
        };

        instance.on('load', handleLoad);
        instance.on('error', handleError);

        return () => {
          disposed = true;
          instance.off('load', handleLoad);
          instance.off('error', handleError);

          controlInstances.forEach((control) => {
            try {
              instance.removeControl(control);
            } catch (controlError) {
              console.warn('Failed to remove MapLibre control:', controlError);
            }
          });

          try {
            instance.remove();
          } catch (removeError) {
            console.warn('Failed to dispose MapLibre instance:', removeError);
          }

          mapRef.current = null;
          setMap(null);
          setIsLoaded(false);
        };
      } catch (error) {
        const err = error instanceof Error ? error : new Error('Failed to initialise MapLibre map');
        if (!disposed) {
          onError?.(err);
        }
      }
    };

    const cleanup = initializeMap();
    return cleanup;
  }, [containerRef, mapOptions, controls, onLoad, onError]);

  return { map, isLoaded };
}

export default useMapInstance;
