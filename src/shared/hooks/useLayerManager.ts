import { useEffect, useMemo } from 'react';
import maplibregl from 'maplibre-gl';
import { LayerManager, type LayerManagerInstance } from '../lib/map/LayerManager';

export function useLayerManager(map: maplibregl.Map | null): LayerManagerInstance | null {
  const manager = useMemo(() => {
    if (!map) return null;
    return new LayerManager(map);
  }, [map]);

  useEffect(() => () => {
    manager?.dispose();
  }, [manager]);

  return manager;
}

export default useLayerManager;
