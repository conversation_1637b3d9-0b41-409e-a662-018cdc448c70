export const formatHeading = (hdg?: number) => {
  if (typeof hdg !== 'number' || Number.isNaN(hdg) || hdg === 511) return '—';
  return `${Math.round(hdg)}°`;
};

export const formatSog = (sog?: number) => {
  const safeSpeed = typeof sog === 'number' && Number.isFinite(sog) ? Math.max(sog, 0) : 0;
  return `${safeSpeed.toFixed(1)} kn`;
};

export const formatCog = (cog?: number) => {
  if (typeof cog !== 'number' || Number.isNaN(cog)) return '—';
  return `${Math.round(cog)}°`;
};
