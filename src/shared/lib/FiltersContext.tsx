import React, { createContext, useContext, useMemo, useState } from 'react';
import { VESSEL_CATEGORIES } from '../types/vesselTypeDefinitions';

export type VesselType = typeof VESSEL_CATEGORIES[number];

export type VesselNavStatus =
  | 'under_way' | 'at_anchor' | 'not_under_command' | 'restricted' | 'constrained'
  | 'moored' | 'aground' | 'fishing' | 'sailing' | 'dangerous_goods_9' | 'dangerous_goods_10'
  | 'towing_astern' | 'pushing_ahead' | 'reserved_13' | 'emergency_sart' | 'undefined';

export type DeviceType = 'sealite' | 'lighthouse' | 'beacon' | 'marker';
export type DeviceStatus = 'operational' | 'maintenance' | 'offline';

export interface FiltersState {
  deviceTypes: DeviceType[];
  deviceStatuses: DeviceStatus[];
  vesselTypes: VesselType[];
  vesselNavStatuses: VesselNavStatus[];
}

interface FiltersContextType {
  filters: FiltersState;
  setFilters: (next: Partial<FiltersState>) => void;
  clearFilters: () => void;
}

const defaultState: FiltersState = {
  deviceTypes: [],
  deviceStatuses: [],
  vesselTypes: [],
  vesselNavStatuses: [],
};

const FiltersContext = createContext<FiltersContextType>({
  filters: defaultState,
  setFilters: () => {},
  clearFilters: () => {},
});

export const useFilters = () => useContext(FiltersContext);

export const FiltersProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<FiltersState>(defaultState);

  const value = useMemo<FiltersContextType>(() => ({
    filters: state,
    setFilters: (next) => setState(prev => ({ ...prev, ...next })),
    clearFilters: () => setState(defaultState),
  }), [state]);

  return (
    <FiltersContext.Provider value={value}>
      {children}
    </FiltersContext.Provider>
  );
};

