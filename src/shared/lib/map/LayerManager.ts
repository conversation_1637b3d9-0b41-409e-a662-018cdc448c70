import maplibregl from 'maplibre-gl';

type MapImageInput =
  | HTMLImageElement
  | ImageBitmap
  | ImageData
  | { width: number; height: number; data: Uint8Array | Uint8ClampedArray }
  | maplibregl.StyleImage;

type LayerEventType = Parameters<maplibregl.Map['on']>[0];

type LayerEventHandler = Parameters<maplibregl.Map['on']>[2];

type MapEventHandler = Parameters<maplibregl.Map['on']>[1];

interface EventBinding {
  type: LayerEventType;
  layerId?: string;
  handler: LayerEventHandler | MapEventHandler;
}

export class LayerManager {
  private map: maplibregl.Map;
  private ownedSources = new Set<string>();
  private ownedLayers = new Set<string>();
  private ownedImages = new Set<string>();
  private eventBindings: EventBinding[] = [];

  constructor(map: maplibregl.Map) {
    this.map = map;
  }

  ensureGeoJSONSource(id: string, source: Omit<maplibregl.GeoJSONSourceRaw, 'type'> = {}): maplibregl.GeoJSONSource {
    const existing = this.map.getSource(id) as maplibregl.GeoJSONSource | undefined;
    if (existing) {
      if (source.data) {
        existing.setData(source.data as GeoJSON.FeatureCollection);
      }
      return existing;
    }

    const { data, ...rest } = source;
    this.map.addSource(id, {
      type: 'geojson',
      data: (data as GeoJSON.FeatureCollection) ?? { type: 'FeatureCollection', features: [] },
      ...rest,
    } as maplibregl.GeoJSONSourceRaw);

    this.ownedSources.add(id);
    return this.map.getSource(id) as maplibregl.GeoJSONSource;
  }

  updateGeoJSONSource(id: string, data: GeoJSON.FeatureCollection) {
    const source = this.map.getSource(id) as maplibregl.GeoJSONSource | undefined;
    if (source) {
      source.setData(data);
    }
  }

  ensureLayer(layer: maplibregl.AnyLayer, beforeId?: string) {
    if (this.map.getLayer(layer.id)) {
      return;
    }
    this.map.addLayer(layer, beforeId);
    this.ownedLayers.add(layer.id);
  }

  removeLayer(id: string) {
    if (this.map.getLayer(id)) {
      this.map.removeLayer(id);
      this.ownedLayers.delete(id);
    }
  }

  addImage(id: string, image: MapImageInput, options?: maplibregl.AddImageOptions) {
    if (this.map.hasImage(id)) {
      return;
    }
    this.map.addImage(id, image, options);
    this.ownedImages.add(id);
  }

  removeImage(id: string) {
    if (this.map.hasImage(id)) {
      this.map.removeImage(id);
      this.ownedImages.delete(id);
    }
  }

  bindLayerEvent(layerId: string, type: LayerEventType, handler: LayerEventHandler) {
    this.map.on(type, layerId, handler);
    const binding: EventBinding = { type, layerId, handler };
    return () => this.unbind(binding);
  }

  bindMapEvent(type: LayerEventType, handler: MapEventHandler) {
    this.map.on(type, handler as LayerEventHandler);
    const binding: EventBinding = { type, handler };
    this.eventBindings.push(binding);
    return () => this.unbind(binding);
  }

  onceLayerEvent(layerId: string, type: LayerEventType, handler: LayerEventHandler) {
    const wrapped: LayerEventHandler = ((event: unknown) => {
      handler(event as never);
      this.map.off(type, layerId, wrapped as LayerEventHandler);
    }) as LayerEventHandler;
    this.map.on(type, layerId, wrapped);
    const binding: EventBinding = { type, layerId, handler: wrapped };
    this.eventBindings.push(binding);
    return () => this.unbind(binding);
  }

  private unbind(binding: EventBinding) {
    const { type, layerId, handler } = binding;
    try {
      if (layerId) {
        this.map.off(type, layerId, handler as LayerEventHandler);
      } else {
        this.map.off(type, handler as MapEventHandler);
      }
    } catch (error) {
      console.warn('Failed to unbind map event', { type, layerId, error });
    }
    this.eventBindings = this.eventBindings.filter((entry) => entry !== binding);
  }

  dispose() {
    // Remove bound events
    this.eventBindings.forEach((binding) => {
      this.unbind(binding);
    });
    this.eventBindings = [];

    // Remove layers we own
    this.ownedLayers.forEach((layerId) => {
      try {
        if (this.map.getLayer(layerId)) {
          this.map.removeLayer(layerId);
        }
      } catch (error) {
        console.warn('Failed to remove map layer', { layerId, error });
      }
    });
    this.ownedLayers.clear();

    // Remove sources we own
    this.ownedSources.forEach((sourceId) => {
      try {
        if (this.map.getSource(sourceId)) {
          this.map.removeSource(sourceId);
        }
      } catch (error) {
        console.warn('Failed to remove map source', { sourceId, error });
      }
    });
    this.ownedSources.clear();

    // Remove images we own
    this.ownedImages.forEach((imageId) => {
      try {
        if (this.map.hasImage(imageId)) {
          this.map.removeImage(imageId);
        }
      } catch (error) {
        console.warn('Failed to remove map image', { imageId, error });
      }
    });
    this.ownedImages.clear();
  }
}

export type LayerManagerInstance = LayerManager;
