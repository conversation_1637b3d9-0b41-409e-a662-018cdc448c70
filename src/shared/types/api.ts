export interface ApiSuccessResponse<T> {
  success: true;
  data: T;
  message: string;
  timestamp: string;
  statusCode: number;
}

export interface ApiErrorResponse {
  success: false;
  message: string;
  error?: string;
  timestamp: string;
  statusCode: number;
  path?: string;
  errors?: Record<string, unknown>;
}

export type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

export interface UserSummary {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: string;
}

export interface UserProfile extends UserSummary {
  isActive: boolean;
  lastLogin: string | null;
  createdAt: string;
}

export interface LoginRequest {
  emailOrUsername: string;
  password: string;
}

export interface LoginPayload {
  accessToken: string;
  user: UserSummary;
}

export type LoginResponse = ApiSuccessResponse<LoginPayload>;

export type ProfileResponse = ApiSuccessResponse<UserProfile>;

export interface VesselApiResponse {
  mmsi: string;
  shipname: string;
  imo: string | null;
  callsign: string | null;
  class: string | null;
  vesselType: string | null;
  vesselIcon: string | null;
  length: number | null;
  width: number | null;
  lastTimestamp: string | null;
  lastLat: number | null;
  lastLon: number | null;
  lastSog: number | null;
  lastCog: number | null;
  lastHdg: number | null;
  lastNavstatus: string | null;
}

export interface VesselSummary {
  mmsi: string;
  shipname: string;
  imo: string | null;
  callsign: string | null;
  vesselClass: string | null;
  vesselType: string | null;
  vesselIcon: string | null;
  length: number | null;
  width: number | null;
  lastTimestamp: string | null;
  lastLat: number | null;
  lastLon: number | null;
  lastSog: number | null;
  lastCog: number | null;
  lastHdg: number | null;
  lastNavstatus: string | null;
}

export interface VesselListPayload {
  vessels: VesselApiResponse[];
  total: number;
  page: number;
  limit: number;
}

export type VesselListResponse = ApiSuccessResponse<VesselListPayload>;

// Vessel Track Types
export interface VesselLogPosition {
  lat: string;
  lon: string;
  sog: string;
  cog: string;
  hdg: number;
  navstatus: number;
}

export interface VesselLogEntry {
  id: string;
  timestamp: string;
  aistype: number;
  channel: string;
  position: VesselLogPosition;
  source: string;
}

export interface VesselLogPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  itemCount: number;
}

export interface VesselLogsPayload {
  mmsi: string;
  dateRange: {
    from: string;
    to: string;
  };
  pagination: VesselLogPagination;
  logs: VesselLogEntry[];
}

export type VesselLogsResponse = ApiSuccessResponse<VesselLogsPayload>;
