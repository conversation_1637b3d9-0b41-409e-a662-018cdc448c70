export class ApiError extends Error {
  status: number;
  data: unknown;

  constructor(message: string, status: number, data: unknown) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

type Primitive = string | number | boolean | null | undefined;

export interface RequestOptions {
  method?: string;
  headers?: HeadersInit;
  body?: unknown;
  query?: Record<string, Primitive>;
  signal?: AbortSignal;
  withAuth?: boolean;
}

let accessToken: string | null = null;

export const setAccessToken = (token: string | null) => {
  accessToken = token;
};

const defaultBaseUrl = import.meta.env.VITE_API_BASE_URL;

const buildUrl = (path: string, query?: RequestOptions['query']) => {
  const base = defaultBaseUrl?.replace(/\/$/, '') ?? '';
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  const url = new URL(`${base}${normalizedPath}`);

  if (query) {
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, String(value));
      }
    });
  }

  return url.toString();
};

const prepareBody = (body: unknown, headers: Headers) => {
  if (body === undefined || body === null) {
    return undefined;
  }

  if (
    typeof body === 'string' ||
    body instanceof FormData ||
    body instanceof Blob ||
    body instanceof ArrayBuffer ||
    ArrayBuffer.isView(body) ||
    body instanceof URLSearchParams
  ) {
    return body as BodyInit;
  }

  if (!headers.has('Content-Type')) {
    headers.set('Content-Type', 'application/json');
  }

  return JSON.stringify(body);
};

const mergeHeaders = (headers?: HeadersInit): Headers => {
  const merged = new Headers(headers);
  if (accessToken) {
    merged.set('Authorization', `Bearer ${accessToken}`);
  }
  return merged;
};

export async function request<T>(path: string, options: RequestOptions = {}): Promise<T> {
  if (!defaultBaseUrl) {
    throw new Error('VITE_API_BASE_URL is not defined. Check your environment configuration.');
  }

  const { method = 'GET', body, headers, withAuth = true, signal, query } = options;
  const requestHeaders = mergeHeaders(headers);

  if (!withAuth) {
    requestHeaders.delete('Authorization');
  }

  const response = await fetch(buildUrl(path, query), {
    method,
    headers: requestHeaders,
    body: prepareBody(body, requestHeaders),
    signal,
  });

  const contentType = response.headers.get('Content-Type');
  const isJson = contentType?.includes('application/json');
  const payload = isJson ? await response.clone().json().catch(() => undefined) : await response.text();

  if (!response.ok) {
    let message = response.statusText || 'Request failed';
    
    if (isJson && payload && typeof payload === 'object') {
      // Prioritize 'error' field over 'message' for more specific error details
      if ('error' in payload && typeof payload.error === 'string') {
        message = payload.error;
      } else if ('message' in payload && typeof payload.message === 'string') {
        message = payload.message;
      }
    }
    
    throw new ApiError(message, response.status, payload);
  }

  if (response.status === 204) {
    return undefined as T;
  }

  if (isJson) {
    return payload as T;
  }

  return (payload as unknown) as T;
}
