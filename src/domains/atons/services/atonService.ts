import { request } from '@/shared/api/apiClient';
import type { AtonApiItem, AtonListResponse } from '../types/aton';

const DEFAULT_LIMIT = 100;

const getAtonKey = (item: AtonApiItem) => item.id ?? item.mmsi;

export interface FetchAtonsParams {
  limit?: number;
  signal?: AbortSignal;
}

export interface FetchAtonsResult {
  atons: AtonApiItem[];
  total: number;
  pagination: {
    totalPages: number;
    itemCount: number;
  };
}

export const atonService = {
  async fetchAtons({ limit = DEFAULT_LIMIT, signal }: FetchAtonsParams = {}): Promise<FetchAtonsResult> {
    const aggregated = new Map<string, AtonApiItem>();

    let page = 1;
    let shouldContinue = true;
    let total = 0;
    let totalPages = 0;
    let itemCount = 0;

    while (shouldContinue) {
      const response = await request<AtonListResponse>('/aton', {
        method: 'GET',
        query: {
          page,
          limit,
        },
        signal,
      });

      const payload = response.data;

      total = payload.total;
      totalPages = payload.totalPages;
      itemCount = payload.itemCount;

      payload.data.forEach((item) => {
        const key = getAtonKey(item);
        if (!key) return;
        aggregated.set(key, item);
      });

      shouldContinue = payload.hasNext && page < payload.totalPages;
      page += 1;
    }

    return {
      atons: Array.from(aggregated.values()),
      total,
      pagination: {
        totalPages,
        itemCount,
      },
    };
  },
};


