import { create } from 'zustand';
import { atonService } from '../services/atonService';
import { atonApiItemToInfo, sortAtonsByTimestampDesc } from '../utils/atonHelpers';
import type { AtonInfo } from '../types/aton';

interface AtonState {
  atonMap: Record<string, AtonInfo>;
  atonList: AtonInfo[];
  total: number;
  isLoading: boolean;
  error: string | null;
  lastFetchedAt: string | null;
  selectedAtonId: string | null;
  fetchAtons: () => Promise<AtonInfo[]>;
  setSelectedAton: (id: string | null) => void;
  clearSelectedAton: () => void;
  reset: () => void;
}

let currentController: AbortController | null = null;

export const useAtonStore = create<AtonState>((set, get) => ({
  atonMap: {},
  atonList: [],
  total: 0,
  isLoading: false,
  error: null,
  lastFetchedAt: null,
  selectedAtonId: null,

  async fetchAtons() {
    if (currentController) {
      currentController.abort();
    }

    const controller = new AbortController();
    currentController = controller;

    set({ isLoading: true, error: null });

    try {
      const { atons, total } = await atonService.fetchAtons({
        signal: controller.signal,
      });

      if (controller.signal.aborted) {
        return [];
      }

      const infos = atons
        .map(atonApiItemToInfo)
        .filter((item): item is AtonInfo => item !== null);

      infos.sort(sortAtonsByTimestampDesc);

      const atonMap = infos.reduce<Record<string, AtonInfo>>((acc, info) => {
        acc[info.id] = info;
        return acc;
      }, {});

      const lastFetchedAt = new Date().toISOString();

      const previousSelected = get().selectedAtonId;
      const nextSelected = previousSelected && atonMap[previousSelected] ? previousSelected : null;

      set({
        atonMap,
        atonList: infos,
        total,
        isLoading: false,
        error: null,
        lastFetchedAt,
        selectedAtonId: nextSelected,
      });

      return infos;
    } catch (error) {
      if (controller.signal.aborted) {
        return [];
      }

      const message = error instanceof Error ? error.message : 'Unable to load AtoN data';
      set({ error: message, isLoading: false });
      throw error;
    } finally {
      if (currentController === controller) {
        currentController = null;
      }
    }
  },

  setSelectedAton(id) {
    set((state) => {
      if (!id) {
        return { selectedAtonId: null };
      }

      if (!state.atonMap[id]) {
        return { selectedAtonId: null };
      }

      return { selectedAtonId: id };
    });
  },

  clearSelectedAton() {
    set({ selectedAtonId: null });
  },

  reset() {
    if (currentController) {
      currentController.abort();
      currentController = null;
    }

    set({
      atonMap: {},
      atonList: [],
      total: 0,
      isLoading: false,
      error: null,
      lastFetchedAt: null,
      selectedAtonId: null,
    });
  },
}));


