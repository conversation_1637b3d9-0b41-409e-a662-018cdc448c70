import type { AtonApiItem, AtonInfo } from '../types/aton';

const toNumber = (value: unknown): number | null => {
  if (value == null) return null;
  if (typeof value === 'number') {
    return Number.isFinite(value) ? value : null;
  }
  if (typeof value === 'string' && value.trim().length > 0) {
    const parsed = Number.parseFloat(value);
    return Number.isFinite(parsed) ? parsed : null;
  }
  return null;
};

const toBoolean = (value: unknown, fallback = false): boolean => {
  if (typeof value === 'boolean') return value;
  if (typeof value === 'string') {
    const normalized = value.trim().toLowerCase();
    if (['true', '1', 'yes'].includes(normalized)) return true;
    if (['false', '0', 'no'].includes(normalized)) return false;
  }
  if (typeof value === 'number') {
    return value !== 0;
  }
  return fallback;
};

const toTimestamp = (value: unknown): string | null => {
  if (typeof value === 'string' && value.trim().length > 0) {
    return value;
  }
  return null;
};

const ensureCoordinate = (value: unknown): number | null => {
  const numeric = toNumber(value);
  if (numeric == null) return null;
  if (!Number.isFinite(numeric)) return null;
  return numeric;
};

export const atonApiItemToInfo = (item: AtonApiItem): AtonInfo | null => {
  const lat = ensureCoordinate(item.lat);
  const lon = ensureCoordinate(item.lon);

  // Allow ATONs without coordinates but with valid data
  // Use null for coordinates if none provided, so they can be handled specially
  return {
    id: item.id,
    mmsi: item.mmsi,
    name: item.name || 'Unknown AtoN',
    lat: lat ?? 0, // Use 0 as fallback for now, but we'll handle this in the map layers
    lon: lon ?? 0, // Use 0 as fallback for now, but we'll handle this in the map layers
    aidtype: item.aidtype,
    status: item.status,
    channel: item.channel,
    isOffPosition: toBoolean(item.isOffPosition, false),
    isVirtual: toBoolean(item.isVirtual, false),
    epfd: item.epfd,
    dimA: toNumber(item.dimA),
    dimB: toNumber(item.dimB),
    dimC: toNumber(item.dimC),
    dimD: toNumber(item.dimD),
    timestamp: toTimestamp(item.lastTimestamp),
    createdAt: item.createdAt,
    updatedAt: item.updatedAt,
  };
};

export const sortAtonsByTimestampDesc = (a: AtonInfo, b: AtonInfo) => {
  const aTime = a.timestamp ? Date.parse(a.timestamp) : 0;
  const bTime = b.timestamp ? Date.parse(b.timestamp) : 0;
  return bTime - aTime;
};


