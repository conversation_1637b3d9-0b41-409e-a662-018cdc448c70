import type { ApiSuccessResponse } from '@/shared/types/api';

export interface AtonApiItem {
  id: string;
  channel: string | null;
  mmsi: string;
  name: string;
  aidtype: string | null;
  lon: string | number | null;
  lat: string | number | null;
  isOffPosition: boolean;
  isVirtual: boolean;
  epfd: string | null;
  dimA: string | number | null;
  dimB: string | number | null;
  dimC: string | number | null;
  dimD: string | number | null;
  status: string | null;
  lastTimestamp: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface AtonInfo {
  id: string;
  mmsi: string;
  name: string;
  lat: number;
  lon: number;
  aidtype: string | null;
  status: string | null;
  channel: string | null;
  isOffPosition: boolean;
  isVirtual: boolean;
  epfd: string | null;
  dimA: number | null;
  dimB: number | null;
  dimC: number | null;
  dimD: number | null;
  timestamp: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface AtonMarker {
  marker?: maplibregl.Marker;
  info: AtonInfo;
}

export interface AtonListPayload {
  data: AtonApiItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  itemCount: number;
}

export type AtonListResponse = ApiSuccessResponse<AtonListPayload>;


