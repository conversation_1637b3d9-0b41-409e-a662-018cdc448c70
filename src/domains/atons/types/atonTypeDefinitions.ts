export interface AtonTypeDefinition {
  code: string;
  label: string;
  description: string;
  icon: string;
  category: AtonCategory | null;
  colorClass: string | null;
}

export const ATON_CATEGORIES = [
  'reference',
  'racon',
  'structure',
  'wreck',
  'light',
  'beacon',
  'mark',
] as const;

export type AtonCategory = typeof ATON_CATEGORIES[number];

const map = [
  ['0', 'Default', 'Default, Type of AtoN not specified', 'aton.png', null, null],
  ['1', 'Reference point', 'Reference point', 'aton.png', 'reference', null],
  ['2', 'RACON', 'RACON', 'aton.png', 'racon', null],
  ['3', 'Fixed structures offshore', 'Fixed structures offshore (e.g., oil platforms, wind farms)', 'aton.png', 'structure', null],
  ['4', 'Emergency Wreck Marking Buoy', 'Emergency Wreck Marking Buoy', 'aton.png', 'wreck', null],
  ['5', 'Light, without sectors', 'Light, without sectors', 'aton.png', 'light', null],
  ['6', 'Light, with sectors', 'Light, with sectors', 'aton.png', 'light', null],
  ['7', 'Leading Light Front', 'Leading Light Front', 'aton.png', 'light', null],
  ['8', 'Leading Light Rear', 'Leading Light Rear', 'aton.png', 'light', null],
  ['9', 'Beacon, Cardinal N', 'Beacon, Cardinal N', 'cardinal_n.png', 'beacon', null],
  ['10', 'Beacon, Cardinal E', 'Beacon, Cardinal E', 'cardinal_e.png', 'beacon', null],
  ['11', 'Beacon, Cardinal S', 'Beacon, Cardinal S', 'cardinal_s.png', 'beacon', null],
  ['12', 'Beacon, Cardinal W', 'Beacon, Cardinal W', 'cardinal_w.png', 'beacon', null],
  ['13', 'Beacon, Port hand', 'Beacon, Port hand', 'port_hand.png', 'beacon', null],
  ['14', 'Beacon, Starboard hand', 'Beacon, Starboard hand', 'starboard_hand.png', 'beacon', null],
  ['15', 'Beacon, Preferred Channel port hand', 'Beacon, Preferred Channel port hand', 'port_hand.png', 'beacon', null],
  ['16', 'Beacon, Preferred Channel starboard hand', 'Beacon, Preferred Channel starboard hand', 'starboard_hand.png', 'beacon', null],
  ['17', 'Beacon, Isolated danger', 'Beacon, Isolated danger', 'isolated_danger.png', 'beacon', null],
  ['18', 'Beacon, Safe water', 'Beacon, Safe water', 'safe_water.png', 'beacon', null],
  ['19', 'Beacon, Special mark', 'Beacon, Special mark', 'special.png', 'beacon', null],
  ['20', 'Cardinal Mark N', 'Cardinal Mark N', 'cardinal_n.png', 'mark', null],
  ['21', 'Cardinal Mark E', 'Cardinal Mark E', 'cardinal_e.png', 'mark', null],
  ['22', 'Cardinal Mark S', 'Cardinal Mark S', 'cardinal_s.png', 'mark', null],
  ['23', 'Cardinal Mark W', 'Cardinal Mark W', 'cardinal_w.png', 'mark', null],
  ['24', 'Port hand Mark', 'Port hand Mark', 'port_hand.png', 'mark', null],
  ['25', 'Starboard hand Mark', 'Starboard hand Mark', 'starboard_hand.png', 'mark', null],
  ['26', 'Preferred Channel Port hand', 'Preferred Channel Port hand', 'port_hand.png', 'mark', null],
  ['27', 'Preferred Channel Starboard hand', 'Preferred Channel Starboard hand', 'starboard_hand.png', 'mark', null],
  ['28', 'Isolated danger', 'Isolated danger', 'isolated_danger.png', 'mark', null],
  ['29', 'Safe Water', 'Safe Water', 'safe_water.png', 'mark', null],
  ['30', 'Special Mark', 'Special Mark', 'special.png', 'mark', null],
  ['31', 'Light Vessel/LANBY/Rigs', 'Light Vessel/LANBY/Rigs', 'aton.png', null, null],
] as const;

export const ATON_TYPE_DEFINITIONS: Record<string, AtonTypeDefinition> = Object.fromEntries(
  map.map(([code, label, description, icon, category, colorClass]) => [code, { code, label, description, icon, category, colorClass }])
);

export const getAtonTypeDefinition = (code?: string | number, isVirtual?: boolean) => {
  if (code == null) return undefined;
  const key = typeof code === 'number' ? String(code) : String(code).trim();
  if (!key) return undefined;
  
  const definition = ATON_TYPE_DEFINITIONS[key];
  if (!definition) return undefined;
  
  // Add the correct prefix based on isVirtual
  const prefix = isVirtual ? 'virtual_' : 'real_';
  const iconName = `${prefix}${definition.icon}`;
  
  return {
    ...definition,
    icon: iconName
  };
};
