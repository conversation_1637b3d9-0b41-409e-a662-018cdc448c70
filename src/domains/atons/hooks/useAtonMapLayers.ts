import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import maplibregl from 'maplibre-gl';
import { useAtonStore } from '../store/useAtonStore';
import type { AtonInfo } from '../types/aton';
import { getAtonTypeDefinition, ATON_TYPE_DEFINITIONS } from '../types/atonTypeDefinitions';
import useLayerManager from '@/shared/hooks/useLayerManager';

// Constants
const ATON_ICON_PATH = '/assets/atons';
const DEFAULT_ICON = 'default.png';
export const ATON_LAYER_ID = 'atons-icons';
export const ATON_SELECTION_LAYER_ID = 'atons-selection';
const SOURCE_ID = 'atons';
const MIN_ZOOM = 9;

const isValidCoordinate = (value: number) => Number.isFinite(value) && Math.abs(value) <= 180;

// Note: coordinate validity handled directly in filtering below

const getIconId = (aton: AtonInfo): string => {
  const typeDefinition = getAtonTypeDefinition(aton.aidtype || undefined, aton.isVirtual);
  const iconName = typeDefinition?.icon || DEFAULT_ICON;
  return iconName.replace('.png', '');
};

export const useAtonMapLayers = (map: maplibregl.Map | null) => {
  const atonList = useAtonStore((state) => state.atonList);
  const atonMap = useAtonStore((state) => state.atonMap);
  const selectedAtonId = useAtonStore((state) => state.selectedAtonId);
  const fetchAtons = useAtonStore((state) => state.fetchAtons);
  const setSelectedAton = useAtonStore((state) => state.setSelectedAton);
  const clearSelectedAton = useAtonStore((state) => state.clearSelectedAton);

  const selectedAton = selectedAtonId ? atonMap[selectedAtonId] : undefined;

  const layerManager = useLayerManager(map);
  const iconsLoadedRef = useRef<Set<string>>(new Set());
  const popupRef = useRef<maplibregl.Popup | null>(null);
  const hoverPopupRef = useRef<maplibregl.Popup | null>(null);
  const hoveredAtonRef = useRef<string | null>(null);
  const activeFeatureRef = useRef<maplibregl.MapGeoJSONFeature | null>(null);
  const [iconsLoadingComplete, setIconsLoadingComplete] = useState(false);
  const [layersReady, setLayersReady] = useState(false);

  // Fetch AtoNs on mount
  useEffect(() => {
    fetchAtons().catch((error) => {
      console.error('Failed to fetch AtoNs:', error);
    });
  }, [fetchAtons]);

  // Create GeoJSON from AtoN list
  const atonGeoJSON = useMemo(() => {
    const features = atonList
      .filter((aton) => isValidCoordinate(aton.lat) && isValidCoordinate(aton.lon))
      .map((aton) => ({
        type: 'Feature' as const,
        id: aton.id,
        geometry: {
          type: 'Point' as const,
          coordinates: [aton.lon, aton.lat],
        },
        properties: {
          id: aton.id,
          mmsi: aton.mmsi,
          name: aton.name,
          channel: aton.channel,
          aidtype: aton.aidtype,
          timestamp: aton.timestamp,
          isVirtual: aton.isVirtual,
          iconId: getIconId(aton),
        },
      }));


    return {
      type: 'FeatureCollection' as const,
      features,
    };
  }, [atonList]);

  // Get all unique AtoN icons needed
  const atonIcons = useMemo(() => {
    const icons = new Set<string>([DEFAULT_ICON]);
    
    atonList.forEach((aton) => {
      const typeDefinition = getAtonTypeDefinition(aton.aidtype || undefined, aton.isVirtual);
      const iconName = typeDefinition?.icon || DEFAULT_ICON;
      icons.add(iconName);
    });
    
    return Array.from(icons);
  }, [atonList]);

  // Pre-compute all possible AtoN icon files (real_ and virtual_ variants) for preload
  const allAtonIconFiles = useMemo(() => {
    const baseIconNames = new Set<string>();
    Object.values(ATON_TYPE_DEFINITIONS).forEach((def) => {
      if (def.icon) baseIconNames.add(def.icon);
    });

    const files: string[] = [];
    baseIconNames.forEach((name) => {
      files.push(`real_${name}`);
      files.push(`virtual_${name}`);
    });
    // Also ensure default exists
    files.push(DEFAULT_ICON);
    return files;
  }, []);

  // Helper function to load a single icon
  const loadIcon = useCallback(async (iconFile: string, iconId: string) => {
    if (!map || !layerManager || iconsLoadedRef.current.has(iconId)) {
      return;
    }

    try {
      const iconUrl = `${ATON_ICON_PATH}/${iconFile}`;
      const image = await map.loadImage(iconUrl);
      
      layerManager.addImage(iconId, image.data);
      iconsLoadedRef.current.add(iconId);
    } catch (error) {
      console.warn(`Failed to load AtoN icon: ${iconFile}`, error);

      // Try to load fallback icon
      if (iconFile !== DEFAULT_ICON) {
        try {
          const fallbackImage = await map.loadImage(`${ATON_ICON_PATH}/${DEFAULT_ICON}`);
          
          layerManager.addImage('default', fallbackImage.data);
          iconsLoadedRef.current.add('default');

          layerManager.addImage(iconId, fallbackImage.data);
          iconsLoadedRef.current.add(iconId);
        } catch (fallbackError) {
          console.error(`Failed to load fallback icon for ${iconFile}:`, fallbackError);
        }
      }
    }
  }, [map, layerManager]);

  // Load all AtoN icons into the map
  useEffect(() => {
    if (!map || !layerManager) return;

    const loadIcons = async () => {
      const loadPromises = atonIcons.map(async (iconFile) => {
        const iconId = iconFile.replace('.png', '');
        return loadIcon(iconFile, iconId);
      });

      try {
        await Promise.all(loadPromises);
        
        // Add selection border icon for AtoN
        if (!map.hasImage('aton-selection-border')) {
          const borderCanvas = document.createElement('canvas');
          borderCanvas.width = 200;
          borderCanvas.height = 200;

          const borderCtx = borderCanvas.getContext('2d');
          if (borderCtx) {
            borderCtx.strokeStyle = '#C93333';
            borderCtx.lineWidth = 15;
            borderCtx.setLineDash([20, 20]);
            // Draw border centered in canvas with proper padding for line width
            const padding = borderCtx.lineWidth / 2;
            borderCtx.strokeRect(padding, padding, borderCanvas.width - borderCtx.lineWidth, borderCanvas.height - borderCtx.lineWidth);

            const imageData = borderCtx.getImageData(0, 0, borderCanvas.width, borderCanvas.height);
            layerManager.addImage('aton-selection-border', imageData);
          }
        }

        setIconsLoadingComplete(true);
      } catch (error) {
        console.error('Error loading AtoN icons:', error);
        setIconsLoadingComplete(true);
      }
    };

    if (map.isStyleLoaded()) {
      loadIcons();
    } else {
      map.once('style.load', loadIcons);
    }

    return () => {
      map.off('style.load', loadIcons);
    };
  }, [map, layerManager, atonIcons, loadIcon]);

  // Preload all possible AtoN icons up-front to avoid styleimagemissing warnings
  useEffect(() => {
    if (!map || !layerManager) return;

    const preloadAll = async () => {
      const tasks = allAtonIconFiles.map((iconFile) => {
        const iconId = iconFile.replace('.png', '');
        return loadIcon(iconFile, iconId);
      });
      try {
        await Promise.all(tasks);
      } catch {
        // Non-fatal; missing files will still be handled by styleimagemissing
      }
    };

    if (map.isStyleLoaded()) {
      preloadAll();
    } else {
      map.once('style.load', preloadAll);
    }

    return () => {
      map.off('style.load', preloadAll);
    };
  }, [map, layerManager, allAtonIconFiles, loadIcon]);

  // Add styleimagemissing event handler for AtoN icons
  useEffect(() => {
    if (!map || !layerManager) return;

    const handleStyleImageMissing = (e: { id: string }) => {
      const { id } = e;
      
      // Only handle AtoN icons (real_ or virtual_ prefixes)
      if (!id.startsWith('real_') && !id.startsWith('virtual_')) {
        return;
      }
      
      // Load the missing icon
      const iconFile = `${id}.png`;
      loadIcon(iconFile, id);
    };

    map.on('styleimagemissing', handleStyleImageMissing);

    return () => {
      map.off('styleimagemissing', handleStyleImageMissing);
    };
  }, [map, layerManager, loadIcon]);

  // Overlay functions for selection and hover
  const getOrCreatePopup = useCallback(() => {
    if (!popupRef.current) {
      popupRef.current = new maplibregl.Popup({
        closeButton: false,
        closeOnClick: false,
        offset: [0, -12],
      });
    }
    return popupRef.current;
  }, []);

  const createPopupHTML = useCallback((props: Record<string, unknown>) => {
    const typeDefinition = getAtonTypeDefinition(
      props.aidtype as string, 
      props.isVirtual as boolean
    );
    const typeLabel = typeDefinition?.label || props.aidtype || '—';
    const timestamp = props.timestamp as string | undefined;
    
    return `
      <div class="text-xs">
        <h4 class="font-semibold mb-1">${props.name ?? ''}</h4>
        <div><strong>MMSI:</strong> ${props.mmsi ?? ''} - ${typeLabel}</div>
        <p><strong>Cập nhật:</strong> ${timestamp ? new Date(timestamp).toLocaleString() : 'N/A'}</p>
      </div>
    `;
  }, []);

  const hideOverlay = useCallback(() => {
    if (!map) return;

    if (map.getLayer(ATON_SELECTION_LAYER_ID)) {
      map.setFilter(ATON_SELECTION_LAYER_ID, ['==', ['get', 'id'], '']);
    }

    if (popupRef.current) {
      popupRef.current.remove();
    }
  }, [map]);

  const hideHoverOverlay = useCallback(() => {
    if (!map) return;

    if (hoverPopupRef.current) {
      hoverPopupRef.current.remove();
    }
  }, [map]);

  const showFeatureOverlay = useCallback((feature: maplibregl.MapGeoJSONFeature) => {
    if (!map) return;

    const props = feature.properties as Record<string, unknown> | undefined;
    const id = props?.id as string | undefined;
    if (!id) {
      return;
    }

    // Update selection layer filter
    if (map.getLayer(ATON_SELECTION_LAYER_ID)) {
      map.setFilter(ATON_SELECTION_LAYER_ID, ['==', ['get', 'id'], id]);
    }

    // Show popup
    const coordinates = feature.geometry.type === 'Point'
      ? (feature.geometry.coordinates as [number, number])
      : undefined;

    if (coordinates) {
      const popup = getOrCreatePopup();
      popup
        .setLngLat(coordinates)
        .setHTML(createPopupHTML(props ?? {}))
        .addTo(map);
    }
  }, [map, createPopupHTML, getOrCreatePopup]);

  const showHoverPopup = useCallback((feature: maplibregl.MapGeoJSONFeature) => {
    if (!map) return;

    const props = feature.properties as Record<string, unknown> | undefined;
    const coordinates =
      feature.geometry.type === 'Point'
        ? (feature.geometry.coordinates as [number, number])
        : undefined;

    if (!coordinates) return;

    // Remove existing hover popup
    if (hoverPopupRef.current) {
      hoverPopupRef.current.remove();
    }

    // Create new hover popup
    hoverPopupRef.current = new maplibregl.Popup({
      closeButton: false,
      closeOnClick: false,
      offset: [0, -12],
    });

    hoverPopupRef.current
      .setLngLat(coordinates)
      .setHTML(createPopupHTML(props ?? {}))
      .addTo(map);
  }, [map, createPopupHTML]);

  // Add GeoJSON source and layer only after icons are loaded
  useEffect(() => {
    if (!map || !layerManager || !iconsLoadingComplete) return;

    layerManager.ensureGeoJSONSource(SOURCE_ID, { data: atonGeoJSON });

    const beforeLayer = map.getLayer('vessels-icons') ? 'vessels-icons' : undefined;

    layerManager.ensureLayer({
      id: ATON_LAYER_ID,
      type: 'symbol',
      source: SOURCE_ID,
      minzoom: MIN_ZOOM,
      layout: {
        'icon-image': ['get', 'iconId'],
        'icon-size': 0.2,
        'icon-anchor': 'bottom',
        'icon-allow-overlap': true,
        'icon-ignore-placement': true
      },
      paint: {
        'icon-opacity': 1
      }
    }, beforeLayer);

    layerManager.ensureLayer({
      id: ATON_SELECTION_LAYER_ID,
      type: 'symbol',
      source: SOURCE_ID,
      filter: ['==', ['get', 'id'], ''],
      layout: {
        'icon-image': 'aton-selection-border',
        'icon-size': 0.14,
        'icon-anchor': 'bottom',
        'icon-allow-overlap': true,
        'icon-ignore-placement': true
      },
      paint: {
        'icon-opacity': 1
      }
    });

    setLayersReady(true);
  }, [map, layerManager, atonGeoJSON, iconsLoadingComplete]);

  // Add interactivity for popups and hover effects
  useEffect(() => {
    if (!map || !layerManager || !layersReady) return;

    const onMouseEnter = (e: maplibregl.MapLayerMouseEvent & { features?: maplibregl.MapGeoJSONFeature[] }) => {
      try {
        if (!e.features || !e.features[0]) return;

        const feature = e.features[0];
        hoveredAtonRef.current = feature.properties?.id as string | null;
        map.getCanvas().style.cursor = 'pointer';

        if (selectedAton) {
          showHoverPopup(feature);
        } else {
          showFeatureOverlay(feature);
        }
      } catch (error) {
        console.error('Error in onMouseEnter:', error);
      }
    };

    const onMouseLeave = () => {
      try {
        map.getCanvas().style.cursor = '';
        hoveredAtonRef.current = null;

        hideHoverOverlay();

        if (!selectedAton) {
          hideOverlay();
        }
      } catch (error) {
        console.error('Error in onMouseLeave:', error);
      }
    };

    const onClick = (e: maplibregl.MapLayerMouseEvent & { features?: maplibregl.MapGeoJSONFeature[] }) => {
      try {
        if (!e.features || !e.features[0]) return;

        const feature = e.features[0];
        const id = feature.properties?.id as string | undefined;
        if (!id) return;

        setSelectedAton(id);
        activeFeatureRef.current = feature;
        showFeatureOverlay(feature);
      } catch (error) {
        console.error('Error in onClick:', error);
      }
    };

    const offMouseEnter = layerManager.bindLayerEvent(ATON_LAYER_ID, 'mouseenter', onMouseEnter);
    const offMouseLeave = layerManager.bindLayerEvent(ATON_LAYER_ID, 'mouseleave', onMouseLeave);
    const offLayerClick = layerManager.bindLayerEvent(ATON_LAYER_ID, 'click', onClick);

    const onMapClick = (event: maplibregl.MapMouseEvent) => {
      const features = map.queryRenderedFeatures(event.point, { layers: [ATON_LAYER_ID] });
      if (features.length > 0) {
        return;
      }
      clearSelectedAton();
      activeFeatureRef.current = null;
      hideOverlay();
    };

    const offMapClick = layerManager.bindMapEvent('click', onMapClick);

    return () => {
      offMouseEnter();
      offMouseLeave();
      offLayerClick();
      offMapClick();
      hideHoverOverlay();
    };
  }, [map, layerManager, layersReady, selectedAton, clearSelectedAton, hideOverlay, hideHoverOverlay, setSelectedAton, showFeatureOverlay, showHoverPopup]);

  // Handle selected ATON changes
  useEffect(() => {
    if (!map) return;
    if (!selectedAton) {
      activeFeatureRef.current = null;
      if (!hoveredAtonRef.current) {
        hideOverlay();
      }
      return;
    }

    if (!Number.isFinite(selectedAton.lon) || !Number.isFinite(selectedAton.lat)) {
      clearSelectedAton();
      return;
    }

    // Create feature for selected ATON
    const feature = {
      type: 'Feature' as const,
      id: selectedAton.id,
      geometry: {
        type: 'Point' as const,
        coordinates: [selectedAton.lon, selectedAton.lat],
      },
      properties: {
        id: selectedAton.id,
        mmsi: selectedAton.mmsi,
        name: selectedAton.name,
        channel: selectedAton.channel,
        aidtype: selectedAton.aidtype,
        timestamp: selectedAton.timestamp,
        isVirtual: selectedAton.isVirtual,
        iconId: getIconId(selectedAton),
      },
    } as unknown as maplibregl.MapGeoJSONFeature;

    activeFeatureRef.current = feature;

    if (!hoveredAtonRef.current || hoveredAtonRef.current !== selectedAton.id) {
      showFeatureOverlay(feature);
    }
  }, [map, selectedAton, clearSelectedAton, hideOverlay, showFeatureOverlay]);

  // Update source data when AtoNs change
  useEffect(() => {
    if (!map || !layerManager || !iconsLoadingComplete) return;

    layerManager.updateGeoJSONSource(SOURCE_ID, atonGeoJSON);
  }, [map, layerManager, atonGeoJSON, iconsLoadingComplete]);

  useEffect(() => () => {
    // Ensure popups are removed when hook unmounts
    popupRef.current?.remove();
    hoverPopupRef.current?.remove();
    iconsLoadedRef.current.clear();
  }, []);

  useEffect(() => {
    if (!map) {
      iconsLoadedRef.current.clear();
      setIconsLoadingComplete(false);
      setLayersReady(false);
    }
  }, [map]);

  return {
    atonList,
  };
};
