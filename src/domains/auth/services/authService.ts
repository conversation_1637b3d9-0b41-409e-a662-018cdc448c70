import { request, setAccessToken } from '@/shared/api/apiClient';
import type { LoginRequest, LoginPayload, LoginResponse, ProfileResponse, UserProfile } from '@/shared/types/api';

export const authService = {
  async login(credentials: LoginRequest): Promise<LoginPayload> {
    const response = await request<LoginResponse>('/auth/login', {
      method: 'POST',
      body: credentials,
      withAuth: false,
    });

    setAccessToken(response.data.accessToken);
    return response.data;
  },

  async getProfile(): Promise<UserProfile> {
    const response = await request<ProfileResponse>('/users/profile', {
      method: 'GET',
    });
    return response.data;
  },

  setAccessToken,
  clearSession() {
    setAccessToken(null);
  },
};
