import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { authService } from '../services/authService';
import type { UserProfile, UserSummary } from '@/shared/types/api';
import { ApiError } from '@/shared/api/apiClient';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserProfile | UserSummary | null;
  login: (emailOrUsername: string, password: string) => Promise<void>;
  logout: () => void;
  refreshProfile: () => Promise<UserProfile>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

const SESSION_STORAGE_KEY = 'navaid-session';

interface StoredSession {
  accessToken: string;
  user: UserSummary;
  expiresAt: number;
}

const decodeJwt = (token: string): { exp?: number } | undefined => {
  try {
    const [, payload] = token.split('.');
    if (!payload || typeof atob !== 'function') {
      return undefined;
    }
    const normalized = payload.replace(/-/g, '+').replace(/_/g, '/');
    const padded = normalized.padEnd(normalized.length + (4 - (normalized.length % 4 || 4)) % 4, '=');
    const decoded = atob(padded);
    return JSON.parse(decoded);
  } catch {
    return undefined;
  }
};

const getExpiryFromToken = (token: string): number => {
  const payload = decodeJwt(token);
  if (payload?.exp) {
    return payload.exp * 1000;
  }
  // Fallback to 12 hours if token does not expose exp
  return Date.now() + 12 * 60 * 60 * 1000;
};

const getStorage = () => {
  if (typeof window === 'undefined') {
    return null;
  }
  try {
    return window.localStorage;
  } catch {
    return null;
  }
};

const loadStoredSession = (): StoredSession | null => {
  const storage = getStorage();
  if (!storage) return null;

  const raw = storage.getItem(SESSION_STORAGE_KEY);
  if (!raw) return null;
  try {
    const parsed = JSON.parse(raw) as StoredSession;
    if (!parsed.accessToken || !parsed.user) {
      return null;
    }
    return parsed;
  } catch {
    return null;
  }
};

const storeSession = (session: StoredSession) => {
  const storage = getStorage();
  storage?.setItem(SESSION_STORAGE_KEY, JSON.stringify(session));
};

const clearStoredSession = () => {
  const storage = getStorage();
  storage?.removeItem(SESSION_STORAGE_KEY);
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<UserProfile | UserSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const clearSession = useCallback(() => {
    clearStoredSession();
    authService.clearSession();
    setIsAuthenticated(false);
    setUser(null);
    setIsLoading(false);
  }, []);

  const refreshProfile = useCallback(async (): Promise<UserProfile> => {
    try {
      const profile = await authService.getProfile();
      setUser(profile);
      setIsAuthenticated(true);
      return profile;
    } catch (error) {
      if (error instanceof ApiError && error.status === 401) {
        clearSession();
      }
      throw error;
    }
  }, [clearSession]);

  useEffect(() => {
    let isCancelled = false;

    const initialize = async () => {
      const session = loadStoredSession();
      if (!session) {
        setIsLoading(false);
        return;
      }

      if (session.expiresAt <= Date.now()) {
        clearSession();
        setIsLoading(false);
        return;
      }

      authService.setAccessToken(session.accessToken);
      setIsAuthenticated(true);
      setUser(session.user);

      try {
        await refreshProfile();
      } catch {
        // refreshProfile handles clearing session for unauthorized scenarios
      } finally {
        if (!isCancelled) {
          setIsLoading(false);
        }
      }
    };

    initialize();

    return () => {
      isCancelled = true;
    };
  }, [clearSession, refreshProfile]);

  const login = async (emailOrUsername: string, password: string): Promise<void> => {
    try {
      const data = await authService.login({ emailOrUsername, password });
      const expiresAt = getExpiryFromToken(data.accessToken);
      const session: StoredSession = {
        accessToken: data.accessToken,
        user: data.user,
        expiresAt,
      };

      storeSession(session);
      authService.setAccessToken(session.accessToken);
      setIsAuthenticated(true);
      setUser(data.user);

      try {
        await refreshProfile();
      } catch (error) {
        // If profile fails for reasons other than unauthorized, keep login data
        if (error instanceof ApiError && error.status === 401) {
          throw error;
        }
      }
    } catch (error) {
      // Re-throw any errors from authService.login (including validation errors)
      throw error;
    }
  };

  const logout = () => {
    clearSession();
  };

  const value: AuthContextType = {
    isAuthenticated,
    isLoading,
    user,
    login,
    logout,
    refreshProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 
