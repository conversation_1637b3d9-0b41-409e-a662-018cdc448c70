import { create } from 'zustand';
import { io, Socket } from 'socket.io-client';
import type { Feature, FeatureCollection, Point } from 'geojson';
import { vesselService, FetchRecentVesselsParams } from '../services/vesselService';
import type { AISData, ConnectionStatus, SocketMessage, VesselInfo } from '../types/vessel';
import type { VesselLogEntry } from '@/shared/types/api';
import { summaryToVesselInfo, aisDataToVesselInfo } from '../utils/vesselHelpers';

const SOCKET_URL = import.meta.env.VITE_SOCKET_URL;

let socket: Socket | null = null;
let socketSubscriberCount = 0;

const TWENTY_FOUR_HOURS_MS = 12 * 60 * 60 * 1000;

const defaultLastSeenAfter = () => new Date(Date.now() - TWENTY_FOUR_HOURS_MS).toISOString();

interface FetchOptions extends Partial<Omit<FetchRecentVesselsParams, 'lastSeenAfter'>> {
  lastSeenAfter?: string;
}

interface VesselPointProperties {
  mmsi: string;
  name: string;
  vesselType: string;
  vesselTypeIcon: string;
  navStatus: string;
  hdg: number;
  cog: number;
  sog: number;
  timestamp: string;
  lat: number;
  lon: number;
  iconId: string;
  isAnchor: boolean;
  rotation: number;
}

interface VesselHeatmapProperties {
  mmsi: string;
  vesselType: string;
  navStatus: string;
  lat: number;
  lon: number;
  weight: number;
}

export type VesselPointFeatureCollection = FeatureCollection<Point, VesselPointProperties>;
export type VesselHeatmapFeatureCollection = FeatureCollection<Point, VesselHeatmapProperties>;

interface VesselState {
  vesselMap: Record<string, VesselInfo>;
  vesselList: VesselInfo[];
  total: number;
  isLoading: boolean;
  error: string | null;
  lastFetchedAt: string | null;
  connectionStatus: ConnectionStatus;
  selectedVesselId: string | null;
  vesselTracks: Record<string, VesselLogEntry[]>;
  isLoadingTrack: boolean;
  trackError: string | null;
  vesselFeatureCollection: VesselPointFeatureCollection;
  vesselHeatmapCollection: VesselHeatmapFeatureCollection;
  // Playback state
  isPlaybackMode: boolean;
  playbackVesselId: string | null;
  playbackData: VesselLogEntry[];
  playbackCurrentIndex: number;
  playbackIsPlaying: boolean;
  playbackSpeed: number;
  fetchRecentVessels: (options?: FetchOptions) => Promise<VesselInfo[]>;
  fetchVesselTrack: (mmsi: string) => Promise<void>;
  clearVesselTrack: (mmsi: string) => void;
  upsertVessel: (vessel: VesselInfo) => void;
  startSocket: () => void;
  stopSocket: () => void;
  reset: () => void;
  setSelectedVessel: (mmsi: string | null) => void;
  clearSelectedVessel: () => void;
  // Playback actions
  startPlayback: (mmsi: string, fromDate: string, toDate: string) => Promise<void>;
  stopPlayback: () => void;
  setPlaybackCurrentIndex: (index: number | ((prev: number) => number)) => void;
  setPlaybackIsPlaying: (isPlaying: boolean) => void;
  setPlaybackSpeed: (speed: number) => void;
  updatePlaybackVesselPosition: (lat: number, lon: number, bearing?: number) => void;
}

const sortByTimestampDesc = (a: VesselInfo, b: VesselInfo) => {
  const aTime = a.timestamp ? Date.parse(a.timestamp) : 0;
  const bTime = b.timestamp ? Date.parse(b.timestamp) : 0;
  return bTime - aTime;
};

const isValidLatitude = (value: unknown): value is number =>
  typeof value === 'number' && Number.isFinite(value) && value >= -90 && value <= 90;

const isValidLongitude = (value: unknown): value is number =>
  typeof value === 'number' && Number.isFinite(value) && value >= -180 && value <= 180;

const withAnchorSuffix = (icon: string, isAnchor: boolean) => {
  if (!isAnchor) return icon;
  const dotIndex = icon.lastIndexOf('.');
  if (dotIndex === -1) return `${icon}_anchor`;
  return `${icon.slice(0, dotIndex)}_anchor${icon.slice(dotIndex)}`;
};

const getVesselIconId = (info: VesselInfo) => {
  const baseIcon = (info.vesselTypeIcon?.trim() || 'default.png').replace(/\s+/g, '');
  const iconName = withAnchorSuffix(baseIcon, info.navStatus === 'at_anchor');
  return iconName.replace('.png', '');
};

const buildVesselFeatureCollections = (vessels: VesselInfo[]) => {
  const pointFeatures: Feature<Point, VesselPointProperties>[] = [];
  const heatmapFeatures: Feature<Point, VesselHeatmapProperties>[] = [];

  for (const vessel of vessels) {
    if (!isValidLatitude(vessel.lat) || !isValidLongitude(vessel.lon)) {
      continue;
    }

    const iconId = getVesselIconId(vessel);
    const isAnchor = vessel.navStatus === 'at_anchor';

    pointFeatures.push({
      type: 'Feature',
      id: vessel.mmsi,
      geometry: {
        type: 'Point',
        coordinates: [vessel.lon, vessel.lat],
      },
      properties: {
        mmsi: vessel.mmsi,
        name: vessel.name || '',
        vesselType: vessel.vesselType || '',
        vesselTypeIcon: vessel.vesselTypeIcon || 'default.png',
        navStatus: vessel.navStatus || '',
        hdg: vessel.hdg ?? 0,
        cog: vessel.cog ?? 0,
        sog: vessel.sog ?? 0,
        timestamp: vessel.timestamp || '',
        lat: vessel.lat,
        lon: vessel.lon,
        iconId,
        isAnchor,
        rotation: vessel.hdg ?? 0,
      },
    });

    heatmapFeatures.push({
      type: 'Feature',
      id: `${vessel.mmsi}-heatmap`,
      geometry: {
        type: 'Point',
        coordinates: [vessel.lon, vessel.lat],
      },
      properties: {
        mmsi: vessel.mmsi,
        vesselType: vessel.vesselType || '',
        navStatus: vessel.navStatus || '',
        lat: vessel.lat,
        lon: vessel.lon,
        weight: vessel.length ? Math.min(vessel.length / 100, 5) : 1,
      },
    });
  }

  return {
    points: {
      type: 'FeatureCollection',
      features: pointFeatures,
    } as VesselPointFeatureCollection,
    heatmap: {
      type: 'FeatureCollection',
      features: heatmapFeatures,
    } as VesselHeatmapFeatureCollection,
  };
};

const EMPTY_VESSEL_FEATURES = buildVesselFeatureCollections([]);

let currentController: AbortController | null = null;

export const useVesselStore = create<VesselState>((set, get) => ({
  vesselMap: {},
  vesselList: [],
  total: 0,
  isLoading: false,
  error: null,
  lastFetchedAt: null,
  connectionStatus: { connected: false, message: 'Disconnected' },
  selectedVesselId: null,
  vesselTracks: {},
  isLoadingTrack: false,
  trackError: null,
  vesselFeatureCollection: EMPTY_VESSEL_FEATURES.points,
  vesselHeatmapCollection: EMPTY_VESSEL_FEATURES.heatmap,
  // Playback state
  isPlaybackMode: false,
  playbackVesselId: null,
  playbackData: [],
  playbackCurrentIndex: 0,
  playbackIsPlaying: false,
  playbackSpeed: 1,

  async fetchRecentVessels(options) {
    if (currentController) {
      currentController.abort();
    }

    const controller = new AbortController();
    currentController = controller;

    set({ isLoading: true, error: null });

    const lastSeenAfter = options?.lastSeenAfter ?? defaultLastSeenAfter();

    try {
      const { vessels, total } = await vesselService.fetchRecentVessels({
        lastSeenAfter,
        hasRecentPosition: options?.hasRecentPosition,
        limit: options?.limit,
        signal: controller.signal,
      });

      if (controller.signal.aborted) {
        return [];
      }

      const infos = vessels
        .map((vessel) => summaryToVesselInfo(vessel))
        .filter((vessel): vessel is VesselInfo => vessel !== null);

      infos.sort(sortByTimestampDesc);

      const vesselMap = infos.reduce<Record<string, VesselInfo>>((acc, vessel) => {
        acc[vessel.mmsi] = vessel;
        return acc;
      }, {});

      const lastFetchedAt = new Date().toISOString();

      const previousSelected = get().selectedVesselId;
      const nextSelected = previousSelected && vesselMap[previousSelected] ? previousSelected : null;

      const { points, heatmap } = buildVesselFeatureCollections(infos);

      set({
        vesselMap,
        vesselList: infos,
        total,
        isLoading: false,
        error: null,
        lastFetchedAt,
        selectedVesselId: nextSelected,
        vesselFeatureCollection: points,
        vesselHeatmapCollection: heatmap,
      });

      return infos;
    } catch (error) {
      if (controller.signal.aborted) {
        return [];
      }

      const message = error instanceof Error ? error.message : 'Không thể tải dữ liệu tàu biển';
      set({ error: message, isLoading: false });
      throw error;
    } finally {
      if (currentController === controller) {
        currentController = null;
      }
    }
  },

  upsertVessel(vessel) {
    set((state) => {
      // Skip updates for playback vessel during playback to prevent position jumping
      if (state.isPlaybackMode && state.playbackVesselId === vessel.mmsi) {
        return state;
      }

      const existing = state.vesselMap[vessel.mmsi];
      const merged = existing
        ? {
            ...existing,
            ...vessel,
            // Skip updating these fields from socket data
            name: existing.name,
            vesselType: existing.vesselType,
            vesselTypeCode: existing.vesselTypeCode,
            vesselTypeLabel: existing.vesselTypeLabel,
            vesselTypeIcon: existing.vesselTypeIcon,
            callsign: existing.callsign,
          }
        : vessel;

      const vesselMap = { ...state.vesselMap, [merged.mmsi]: merged };
      const vesselList = existing
        ? state.vesselList.map((item) => (item.mmsi === merged.mmsi ? merged : item))
        : [...state.vesselList, merged];

      const { points, heatmap } = buildVesselFeatureCollections(vesselList);

      return {
        vesselMap,
        vesselList,
        total: existing ? state.total : state.total + 1,
        vesselFeatureCollection: points,
        vesselHeatmapCollection: heatmap,
      };
    });
  },

  startSocket() {
    if (!SOCKET_URL) {
      console.error('VITE_SOCKET_URL is not configured');
      return;
    }
    socketSubscriberCount = Math.max(socketSubscriberCount + 1, 1);

    if (socket) {
      return;
    }

    set({ connectionStatus: { connected: false, message: 'Connecting...' } });

    socket = io(SOCKET_URL);

    socket.on('connect', () => {
      set({ connectionStatus: { connected: true, message: 'Connected' } });
      socket?.emit('SUB_AIS', {});
    });

    socket.on('disconnect', () => {
      set({ connectionStatus: { connected: false, message: 'Disconnected' } });
    });

    socket.on('connect_error', () => {
      set({ connectionStatus: { connected: false, message: 'Connection Error' } });
    });

    socket.on('error', () => {
      set({ connectionStatus: { connected: false, message: 'Error' } });
    });

    const handleAisPayload = (payload: AISData, timestamp?: string) => {
      const info = aisDataToVesselInfo(payload, timestamp);
      if (info) {
        get().upsertVessel(info);
      }
    };

    const handleSocketMessage = (message: SocketMessage) => {
      try {
        if (message?.type === 'AIS_DATA' && message?.data?.data?.message) {
          const parsed: AISData = JSON.parse(message.data.data.message);
          if (parsed?.lat && parsed?.lon) {
            handleAisPayload(parsed);
          }
        } else if (message?.data?.data) {
          const payload = message.data.data;
          if (payload?.lat && payload?.lon) {
            handleAisPayload(payload as AISData);
          }
        }
      } catch (error) {
        console.error('Error handling vessel socket message:', error);
      }
    };

    socket.on('RES_MSG', handleSocketMessage);
    socket.on('VESSEL_UPDATE', handleSocketMessage);
  },

  stopSocket() {
    socketSubscriberCount = Math.max(socketSubscriberCount - 1, 0);
    if (socket && socketSubscriberCount === 0) {
      socket.removeAllListeners();
      socket.disconnect();
      socket = null;
      set({ connectionStatus: { connected: false, message: 'Disconnected' } });
    }
  },

  reset() {
    if (currentController) {
      currentController.abort();
      currentController = null;
    }
    set({
      vesselMap: {},
      vesselList: [],
      total: 0,
      isLoading: false,
      error: null,
      lastFetchedAt: null,
      connectionStatus: { connected: false, message: 'Disconnected' },
      selectedVesselId: null,
      vesselTracks: {},
      isLoadingTrack: false,
      trackError: null,
      vesselFeatureCollection: EMPTY_VESSEL_FEATURES.points,
      vesselHeatmapCollection: EMPTY_VESSEL_FEATURES.heatmap,
    });
  },

  setSelectedVessel(mmsi) {
    set((state) => {
      if (!mmsi) {
        return { selectedVesselId: null };
      }

      if (!state.vesselMap[mmsi]) {
        return { selectedVesselId: null };
      }

      // get().fetchVesselTrack(mmsi);

      return { selectedVesselId: mmsi };
    });
  },

  clearSelectedVessel() {
    const currentSelected = get().selectedVesselId;
    if (currentSelected) {
      get().clearVesselTrack(currentSelected);
    }
    set({ selectedVesselId: null });
  },

  async fetchVesselTrack(_mmsi) {
    void _mmsi;
    set({ isLoadingTrack: true, trackError: null });
    
    try {
      // const { logs } = await vesselService.fetchVesselLogs({
      //   mmsi,
      //   // Fetch last 24 hours
      //   from: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      //   to: new Date().toISOString(),
      // });

      // set((state) => ({
      //   vesselTracks: {
      //     ...state.vesselTracks,
      //     [mmsi]: logs,
      //   },
      //   isLoadingTrack: false,
      //   trackError: null,
      // }));

      set({ isLoadingTrack: false, trackError: null });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to fetch vessel track';
      set({ isLoadingTrack: false, trackError: message });
      console.error('Error fetching vessel track:', error);
    }
  },

  clearVesselTrack(mmsi) {
    set((state) => {
      const newTracks = { ...state.vesselTracks };
      delete newTracks[mmsi];
      return { vesselTracks: newTracks };
    });
  },

  async startPlayback(mmsi, fromDate, toDate) {
    set({ isLoadingTrack: true, trackError: null });
    
    try {
      const { logs } = await vesselService.fetchVesselLogs({
        mmsi,
        from: fromDate,
        to: toDate,
        sortBy: 'timestamp',
        sortOrder: 'ASC', // Sort ascending for playback
      });

      if (logs.length === 0) {
        throw new Error('No data found for the selected date range');
      }

      set({
        isPlaybackMode: true,
        playbackVesselId: mmsi,
        playbackData: logs,
        playbackCurrentIndex: 0,
        playbackIsPlaying: true, // Automatically start playing
        isLoadingTrack: false,
        trackError: null,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to fetch playback data';
      set({ isLoadingTrack: false, trackError: message });
      console.error('Error starting playback:', error);
      throw error;
    }
  },

  stopPlayback() {
    set({
      isPlaybackMode: false,
      playbackVesselId: null,
      playbackData: [],
      playbackCurrentIndex: 0,
      playbackIsPlaying: false,
    });
  },

  setPlaybackCurrentIndex(indexOrUpdater) {
    set((state) => {
      const newIndex = typeof indexOrUpdater === 'function' 
        ? indexOrUpdater(state.playbackCurrentIndex)
        : indexOrUpdater;
      
      const clampedIndex = Math.max(0, Math.min(newIndex, state.playbackData.length - 1));
      
      return {
        playbackCurrentIndex: clampedIndex
      };
    });
  },

  setPlaybackIsPlaying(isPlaying) {
    set({ playbackIsPlaying: isPlaying });
  },

  setPlaybackSpeed(speed) {
    set({ playbackSpeed: Math.max(0.1, Math.min(speed, 10)) });
  },

  updatePlaybackVesselPosition(lat, lon, bearing) {
    set((state) => {
      if (!state.isPlaybackMode || !state.playbackVesselId) return state;

      const vessel = state.vesselMap[state.playbackVesselId];
      if (!vessel) return state;

      const updatedVessel = {
        ...vessel,
        lat,
        lon,
        hdg: bearing ?? vessel.hdg,
      };

      const vesselMap = {
        ...state.vesselMap,
        [state.playbackVesselId]: updatedVessel,
      };

      const vesselList = state.vesselList.map((item) =>
        item.mmsi === updatedVessel.mmsi ? updatedVessel : item
      );

      const { points, heatmap } = buildVesselFeatureCollections(vesselList);

      return {
        vesselMap,
        vesselList,
        vesselFeatureCollection: points,
        vesselHeatmapCollection: heatmap,
      };
    });
  },
}));
