import type { VesselType, VesselNavStatus } from '@/shared/lib/FiltersContext';
import type { VesselSummary } from '@/shared/types/api';
import type { AISData, VesselInfo } from '../types/vessel';
import { getVesselFlagCodeFromMMSI } from '../types/vesselFlagCode';
import { getVesselTypeDefinition } from '../types/vesselTypeDefinitions';

const toNumber = (value: unknown): number | null => {
  if (value == null) return null;
  if (typeof value === 'number') {
    return Number.isFinite(value) ? value : null;
  }
  if (typeof value === 'string' && value.trim().length > 0) {
    const parsed = Number.parseFloat(value);
    return Number.isFinite(parsed) ? parsed : null;
  }
  return null;
};

export const resolveVesselType = (code?: string | number) => {
  const fallback = {
    category: 'unspecified' as VesselType,
    code: undefined as string | undefined,
    label: undefined as string | undefined,
    icon: 'default.png',
  };

  if (code == null) {
    return fallback;
  }

  const key = typeof code === 'number' ? String(code) : String(code).trim();
  if (!key) {
    return fallback;
  }

  const def = getVesselTypeDefinition(key);

  if (!def) {
    return {
      category: fallback.category,
      code: key,
      label: undefined,
      icon: fallback.icon,
    };
  }

  return {
    category: def.category,
    code: def.code,
    label: def.label,
    icon: def.icon || fallback.icon,
  };
};

export function mapAisTypeToVesselType(aisType?: number): VesselType | undefined {
  return resolveVesselType(aisType).category;
}

export function mapAisNavStatusToEnum(nav?: number): VesselNavStatus | undefined {
  switch (nav) {
    case 0: return 'under_way';
    case 1: return 'at_anchor';
    case 2: return 'not_under_command';
    case 3: return 'restricted';
    case 4: return 'constrained';
    case 5: return 'moored';
    case 6: return 'aground';
    case 7: return 'fishing';
    case 8: return 'sailing';
    case 9: return 'dangerous_goods_9';
    case 10: return 'dangerous_goods_10';
    case 11: return 'towing_astern';
    case 12: return 'pushing_ahead';
    case 13: return 'reserved_13';
    case 14: return 'emergency_sart';
    case 15: return 'undefined';
    default: return 'undefined';
  }
}

export function summaryToVesselInfo(summary: VesselSummary): VesselInfo | null {
  if (typeof summary.lastLat !== 'number' || typeof summary.lastLon !== 'number') {
    return null;
  }

  const typeCode = summary.vesselType ?? undefined;
  const vesselTypeInfo = resolveVesselType(typeCode);
  const navStatusCandidate = summary.lastNavstatus != null ? Number.parseInt(String(summary.lastNavstatus), 10) : undefined;
  const navStatus = Number.isFinite(navStatusCandidate ?? NaN) ? (navStatusCandidate as number) : undefined;

  const info: VesselInfo = {
    mmsi: summary.mmsi,
    name: summary.shipname ?? 'Unknown',
    lat: summary.lastLat,
    lon: summary.lastLon,
    cog: summary.lastCog ?? 0,
    sog: summary.lastSog ?? 0,
    hdg: summary.lastHdg ?? undefined,
    timestamp: summary.lastTimestamp ?? new Date().toISOString(),
    aisType: toNumber(typeCode) ?? undefined,
    vesselType: vesselTypeInfo.category,
    vesselTypeCode: vesselTypeInfo.code,
    vesselTypeLabel: vesselTypeInfo.label,
    vesselTypeIcon: vesselTypeInfo.icon,
    navStatus: mapAisNavStatusToEnum(navStatus),
    length: summary.length,
    width: summary.width,
    vesselClass: summary.vesselClass ?? '',
    callsign: summary.callsign,
  };
  const flagDef = getVesselFlagCodeFromMMSI(info.mmsi);
  return {
    ...info,
    flagCode: flagDef.code,
    flagLabel: flagDef.label,
    flagIcon: flagDef.icon,
  };
}

export function aisDataToVesselInfo(aisData: AISData, overrideTimestamp?: string): VesselInfo | null {
  const mmsi = aisData.mmsi || aisData.immsi || aisData.userid;
  if (!mmsi) return null;

  const lat = toNumber(aisData.lat);
  const lon = toNumber(aisData.lon);
  if (lat == null || lon == null) {
    return null;
  }

  const rawSog = aisData.sog ?? (aisData as unknown as { speed?: number | string }).speed;
  const sog = toNumber(rawSog) ?? 0;
  const hdg = toNumber(aisData.hdg) ?? 0;
  const cog = toNumber(aisData.cog) ?? 0;
  const aisTypeCandidate = toNumber(aisData.aistype);
  if (aisTypeCandidate === 21) {
    return null; // skip AtoN markers
  }
  const navStatusCandidate = toNumber((aisData as unknown as { navstatus?: number }).navstatus);
  const explicitVesselType = (aisData as unknown as { vesselType?: string | number }).vesselType;
  const vesselTypeInfo = resolveVesselType(explicitVesselType ?? aisTypeCandidate ?? undefined);
  const shipname = aisData.shipname || aisData.txt || aisData.name || 'Unknown';

  const info: VesselInfo = {
    mmsi,
    name: shipname,
    lat,
    lon,
    cog,
    sog,
    hdg,
    timestamp: overrideTimestamp ?? new Date().toISOString(),
    aisType: aisTypeCandidate ?? undefined,
    vesselType: vesselTypeInfo.category,
    vesselTypeCode: vesselTypeInfo.code,
    vesselTypeLabel: vesselTypeInfo.label,
    vesselTypeIcon: vesselTypeInfo.icon,
    navStatus: mapAisNavStatusToEnum(navStatusCandidate ?? undefined),
    length: null, // AIS data doesn't typically include dimensions
    width: null,
    callsign: aisData.callsign ?? null,
  };
  const flagDef = getVesselFlagCodeFromMMSI(info.mmsi);
  return {
    ...info,
    flagCode: flagDef.code,
    flagLabel: flagDef.label,
    flagIcon: flagDef.icon,
  };
}
