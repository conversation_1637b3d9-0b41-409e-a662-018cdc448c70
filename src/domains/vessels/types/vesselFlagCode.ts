export interface VesselFlagCodeDefinition {
    code: string;
    label: string;
    description: string;
    icon: string;
}

const map = [
    ['201', 'Albania', 'Albania', 'AL'],
    ['202', 'Andorra', 'Andorra', 'AD'],
    ['203', 'Austria', 'Austria', 'AT'],
    ['204', 'Portugal', 'Portugal', 'PT'],
    ['205', 'Belgium', 'Belgium', 'BE'],
    ['206', 'Belarus', 'Belarus', 'BY'],
    ['207', 'Bulgaria', 'Bulgaria', 'BG'],
    ['208', 'Vatican', 'Vatican', 'VA'],
    ['209', 'Cyprus', 'Cyprus', 'CY'],
    ['210', 'Cyprus', 'Cyprus', 'CY'],
    ['211', 'Germany', 'Germany', 'DE'],
    ['212', 'Cyprus', 'Cyprus', 'CY'],
    ['213', 'Georgia', 'Georgia', 'GE'],
    ['214', 'Moldova', 'Moldova', 'MD'],
    ['215', 'Malta', 'Malta', 'MT'],
    ['216', 'Armenia', 'Armenia', 'AM'],
    ['218', 'Germany', 'Germany', 'DE'],
    ['219', 'Denmark', 'Denmark', 'DK'],
    ['220', 'Denmark', 'Denmark', 'DK'],
    ['224', 'Spain', 'Spain', 'ES'],
    ['225', 'Spain', 'Spain', 'ES'],
    ['226', 'France', 'France', 'FR'],
    ['227', 'France', 'France', 'FR'],
    ['228', 'France', 'France', 'FR'],
    ['229', 'Malta', 'Malta', 'MT'],
    ['230', 'Finland', 'Finland', 'FI'],
    ['231', 'Faroe Is', 'Faroe Is', 'FO'],
    ['232', 'United Kingdom', 'United Kingdom', 'GB'],
    ['233', 'United Kingdom', 'United Kingdom', 'GB'],
    ['234', 'United Kingdom', 'United Kingdom', 'GB'],
    ['235', 'United Kingdom', 'United Kingdom', 'GB'],
    ['236', 'Gibraltar', 'Gibraltar', 'GI'],
    ['237', 'Greece', 'Greece', 'GR'],
    ['238', 'Croatia', 'Croatia', 'HR'],
    ['239', 'Greece', 'Greece', 'GR'],
    ['240', 'Greece', 'Greece', 'GR'],
    ['241', 'Greece', 'Greece', 'GR'],
    ['242', 'Morocco', 'Morocco', 'MA'],
    ['243', 'Hungary', 'Hungary', 'HU'],
    ['244', 'Netherlands', 'Netherlands', 'NL'],
    ['245', 'Netherlands', 'Netherlands', 'NL'],
    ['246', 'Netherlands', 'Netherlands', 'NL'],
    ['247', 'Italy', 'Italy', 'IT'],
    ['248', 'Malta', 'Malta', 'MT'],
    ['249', 'Malta', 'Malta', 'MT'],
    ['250', 'Ireland', 'Ireland', 'IE'],
    ['251', 'Iceland', 'Iceland', 'IS'],
    ['252', 'Liechtenstein', 'Liechtenstein', 'LI'],
    ['253', 'Luxembourg', 'Luxembourg', 'LU'],
    ['254', 'Monaco', 'Monaco', 'MC'],
    ['255', 'Portugal', 'Portugal', 'PT'],
    ['256', 'Malta', 'Malta', 'MT'],
    ['257', 'Norway', 'Norway', 'NO'],
    ['258', 'Norway', 'Norway', 'NO'],
    ['259', 'Norway', 'Norway', 'NO'],
    ['261', 'Poland', 'Poland', 'PL'],
    ['262', 'Montenegro', 'Montenegro', 'ME'],
    ['263', 'Portugal', 'Portugal', 'PT'],
    ['264', 'Romania', 'Romania', 'RO'],
    ['265', 'Sweden', 'Sweden', 'SE'],
    ['266', 'Sweden', 'Sweden', 'SE'],
    ['267', 'Slovakia', 'Slovakia', 'SK'],
    ['268', 'San Marino', 'San Marino', 'SM'],
    ['269', 'Switzerland', 'Switzerland', 'CH'],
    ['270', 'Czech Republic', 'Czech Republic', 'CZ'],
    ['271', 'Turkey', 'Turkey', 'TR'],
    ['272', 'Ukraine', 'Ukraine', 'UA'],
    ['273', 'Russia', 'Russia', 'RU'],
    ['274', 'FYR Macedonia', 'FYR Macedonia', 'MK'],
    ['275', 'Latvia', 'Latvia', 'LV'],
    ['276', 'Estonia', 'Estonia', 'EE'],
    ['277', 'Lithuania', 'Lithuania', 'LT'],
    ['278', 'Slovenia', 'Slovenia', 'SI'],
    ['279', 'Serbia', 'Serbia', 'RS'],
    ['301', 'Anguilla', 'Anguilla', 'AI'],
    ['303', 'USA', 'USA', 'US'],
    ['304', 'Antigua Barbuda', 'Antigua Barbuda', 'AG'],
    ['305', 'Antigua Barbuda', 'Antigua Barbuda', 'AG'],
    ['306', 'Curacao', 'Curacao', 'CW'],
    ['307', 'Aruba', 'Aruba', 'AW'],
    ['308', 'Bahamas', 'Bahamas', 'BS'],
    ['309', 'Bahamas', 'Bahamas', 'BS'],
    ['310', 'Bermuda', 'Bermuda', 'BM'],
    ['311', 'Bahamas', 'Bahamas', 'BS'],
    ['312', 'Belize', 'Belize', 'BZ'],
    ['314', 'Barbados', 'Barbados', 'BB'],
    ['316', 'Canada', 'Canada', 'CA'],
    ['319', 'Cayman Is', 'Cayman Is', 'KY'],
    ['321', 'Costa Rica', 'Costa Rica', 'CR'],
    ['323', 'Cuba', 'Cuba', 'CU'],
    ['325', 'Dominica', 'Dominica', 'DM'],
    ['327', 'Dominican Rep', 'Dominican Rep', 'DO'],
    ['329', 'Guadeloupe', 'Guadeloupe', 'GP'],
    ['330', 'Grenada', 'Grenada', 'GD'],
    ['331', 'Greenland', 'Greenland', 'GL'],
    ['332', 'Guatemala', 'Guatemala', 'GT'],
    ['334', 'Honduras', 'Honduras', 'HN'],
    ['336', 'Haiti', 'Haiti', 'HT'],
    ['338', 'USA', 'USA', 'US'],
    ['339', 'Jamaica', 'Jamaica', 'JM'],
    ['341', 'St Kitts Nevis', 'St Kitts Nevis', 'KN'],
    ['343', 'St Lucia', 'St Lucia', 'LC'],
    ['345', 'Mexico', 'Mexico', 'MX'],
    ['347', 'Martinique', 'Martinique', 'MQ'],
    ['348', 'Montserrat', 'Montserrat', 'MS'],
    ['350', 'Nicaragua', 'Nicaragua', 'NI'],
    ['351', 'Panama', 'Panama', 'PA'],
    ['352', 'Panama', 'Panama', 'PA'],
    ['353', 'Panama', 'Panama', 'PA'],
    ['354', 'Panama', 'Panama', 'PA'],
    ['355', 'Panama', 'Panama', 'PA'],
    ['356', 'Panama', 'Panama', 'PA'],
    ['357', 'Panama', 'Panama', 'PA'],
    ['358', 'Puerto Rico', 'Puerto Rico', 'PR'],
    ['359', 'El Salvador', 'El Salvador', 'SV'],
    ['361', 'St Pierre Miquelon', 'St Pierre Miquelon', 'PM'],
    ['362', 'Trinidad Tobago', 'Trinidad Tobago', 'TT'],
    ['364', 'Turks Caicos Is', 'Turks Caicos Is', 'TC'],
    ['366', 'USA', 'USA', 'US'],
    ['367', 'USA', 'USA', 'US'],
    ['368', 'USA', 'USA', 'US'],
    ['369', 'USA', 'USA', 'US'],
    ['370', 'Panama', 'Panama', 'PA'],
    ['371', 'Panama', 'Panama', 'PA'],
    ['372', 'Panama', 'Panama', 'PA'],
    ['373', 'Panama', 'Panama', 'PA'],
    ['374', 'Panama', 'Panama', 'PA'],
    ['375', 'St Vincent Grenadines', 'St Vincent Grenadines', 'VC'],
    ['376', 'St Vincent Grenadines', 'St Vincent Grenadines', 'VC'],
    ['377', 'St Vincent Grenadines', 'St Vincent Grenadines', 'VC'],
    ['378', 'British Virgin Is', 'British Virgin Is', 'VG'],
    ['379', 'US Virgin Is', 'US Virgin Is', 'VI'],
    ['401', 'Afghanistan', 'Afghanistan', 'AF'],
    ['403', 'Saudi Arabia', 'Saudi Arabia', 'SA'],
    ['405', 'Bangladesh', 'Bangladesh', 'BD'],
    ['408', 'Bahrain', 'Bahrain', 'BH'],
    ['410', 'Bhutan', 'Bhutan', 'BT'],
    ['412', 'China', 'China', 'CN'],
    ['413', 'China', 'China', 'CN'],
    ['414', 'China', 'China', 'CN'],
    ['416', 'Taiwan', 'Taiwan', 'TW'],
    ['417', 'Sri Lanka', 'Sri Lanka', 'LK'],
    ['419', 'India', 'India', 'IN'],
    ['422', 'Iran', 'Iran', 'IR'],
    ['423', 'Azerbaijan', 'Azerbaijan', 'AZ'],
    ['425', 'Iraq', 'Iraq', 'IQ'],
    ['428', 'Israel', 'Israel', 'IL'],
    ['431', 'Japan', 'Japan', 'JP'],
    ['432', 'Japan', 'Japan', 'JP'],
    ['434', 'Turkmenistan', 'Turkmenistan', 'TM'],
    ['436', 'Kazakhstan', 'Kazakhstan', 'KZ'],
    ['437', 'Uzbekistan', 'Uzbekistan', 'UZ'],
    ['438', 'Jordan', 'Jordan', 'JO'],
    ['440', 'Korea', 'Korea', 'KR'],
    ['441', 'Korea', 'Korea', 'KR'],
    ['443', 'Palestine', 'Palestine', 'PS'],
    ['445', 'DPR Korea', 'DPR Korea', 'KP'],
    ['447', 'Kuwait', 'Kuwait', 'KW'],
    ['450', 'Lebanon', 'Lebanon', 'LB'],
    ['451', 'Kyrgyz Republic', 'Kyrgyz Republic', 'KG'],
    ['453', 'Macao', 'Macao', 'MO'],
    ['455', 'Maldives', 'Maldives', 'MV'],
    ['457', 'Mongolia', 'Mongolia', 'MN'],
    ['459', 'Nepal', 'Nepal', 'NP'],
    ['461', 'Oman', 'Oman', 'OM'],
    ['463', 'Pakistan', 'Pakistan', 'PK'],
    ['466', 'Qatar', 'Qatar', 'QA'],
    ['468', 'Syria', 'Syria', 'SY'],
    ['470', 'UAE', 'UAE', 'AE'],
    ['471', 'UAE', 'UAE', 'AE'],
    ['472', 'Tajikistan', 'Tajikistan', 'TJ'],
    ['473', 'Yemen', 'Yemen', 'YE'],
    ['475', 'Yemen', 'Yemen', 'YE'],
    ['477', 'Hong Kong', 'Hong Kong', 'HK'],
    ['478', 'Bosnia and Herzegovina', 'Bosnia and Herzegovina', 'BA'],
    ['501', 'Antarctica', 'Antarctica', 'AQ'],
    ['503', 'Australia', 'Australia', 'AU'],
    ['506', 'Myanmar', 'Myanmar', 'MM'],
    ['508', 'Brunei', 'Brunei', 'BN'],
    ['510', 'Micronesia', 'Micronesia', 'FM'],
    ['511', 'Palau', 'Palau', 'PW'],
    ['512', 'New Zealand', 'New Zealand', 'NZ'],
    ['514', 'Cambodia', 'Cambodia', 'KH'],
    ['515', 'Cambodia', 'Cambodia', 'KH'],
    ['516', 'Christmas Is', 'Christmas Is', 'CX'],
    ['518', 'Cook Is', 'Cook Is', 'CK'],
    ['520', 'Fiji', 'Fiji', 'FJ'],
    ['523', 'Cocos Is', 'Cocos Is', 'CC'],
    ['525', 'Indonesia', 'Indonesia', 'ID'],
    ['529', 'Kiribati', 'Kiribati', 'KI'],
    ['531', 'Laos', 'Laos', 'LA'],
    ['533', 'Malaysia', 'Malaysia', 'MY'],
    ['536', 'N Mariana Is', 'N Mariana Is', 'MP'],
    ['538', 'Marshall Is', 'Marshall Is', 'MH'],
    ['540', 'New Caledonia', 'New Caledonia', 'NC'],
    ['542', 'Niue', 'Niue', 'NU'],
    ['544', 'Nauru', 'Nauru', 'NR'],
    ['546', 'French Polynesia', 'French Polynesia', 'PF'],
    ['548', 'Philippines', 'Philippines', 'PH'],
    ['553', 'Papua New Guinea', 'Papua New Guinea', 'PG'],
    ['555', 'Pitcairn Is', 'Pitcairn Is', 'PN'],
    ['557', 'Solomon Is', 'Solomon Is', 'SB'],
    ['559', 'American Samoa', 'American Samoa', 'AS'],
    ['561', 'Samoa', 'Samoa', 'WS'],
    ['563', 'Singapore', 'Singapore', 'SG'],
    ['564', 'Singapore', 'Singapore', 'SG'],
    ['565', 'Singapore', 'Singapore', 'SG'],
    ['566', 'Singapore', 'Singapore', 'SG'],
    ['567', 'Thailand', 'Thailand', 'TH'],
    ['570', 'Tonga', 'Tonga', 'TO'],
    ['572', 'Tuvalu', 'Tuvalu', 'TV'],
    ['574', 'Việt Nam', 'Việt Nam', 'VN'],
    ['576', 'Vanuatu', 'Vanuatu', 'VU'],
    ['577', 'Vanuatu', 'Vanuatu', 'VU'],
    ['578', 'Wallis Futuna Is', 'Wallis Futuna Is', 'WF'],
    ['601', 'South Africa', 'South Africa', 'ZA'],
    ['603', 'Angola', 'Angola', 'AO'],
    ['605', 'Algeria', 'Algeria', 'DZ'],
    ['607', 'St Paul Amsterdam Is', 'St Paul Amsterdam Is', 'TF'],
    ['608', 'Ascension Is', 'Ascension Is', 'IO'],
    ['609', 'Burundi', 'Burundi', 'BI'],
    ['610', 'Benin', 'Benin', 'BJ'],
    ['611', 'Botswana', 'Botswana', 'BW'],
    ['612', 'Cen Afr Rep', 'Cen Afr Rep', 'CF'],
    ['613', 'Cameroon', 'Cameroon', 'CM'],
    ['615', 'Congo', 'Congo', 'CG'],
    ['616', 'Comoros', 'Comoros', 'KM'],
    ['617', 'Cape Verde', 'Cape Verde', 'CV'],
    ['618', 'Antarctica', 'Antarctica', 'AQ'],
    ['619', 'Ivory Coast', 'Ivory Coast', 'CI'],
    ['620', 'Comoros', 'Comoros', 'KM'],
    ['621', 'Djibouti', 'Djibouti', 'DJ'],
    ['622', 'Egypt', 'Egypt', 'EG'],
    ['624', 'Ethiopia', 'Ethiopia', 'ET'],
    ['625', 'Eritrea', 'Eritrea', 'ER'],
    ['626', 'Gabon', 'Gabon', 'GA'],
    ['627', 'Ghana', 'Ghana', 'GH'],
    ['629', 'Gambia', 'Gambia', 'GM'],
    ['630', 'Guinea-Bissau', 'Guinea-Bissau', 'GW'],
    ['631', 'Equ. Guinea', 'Equ. Guinea', 'GQ'],
    ['632', 'Guinea', 'Guinea', 'GN'],
    ['633', 'Burkina Faso', 'Burkina Faso', 'BF'],
    ['634', 'Kenya', 'Kenya', 'KE'],
    ['635', 'Antarctica', 'Antarctica', 'AQ'],
    ['636', 'Liberia', 'Liberia', 'LR'],
    ['637', 'Liberia', 'Liberia', 'LR'],
    ['642', 'Libya', 'Libya', 'LY'],
    ['644', 'Lesotho', 'Lesotho', 'LS'],
    ['645', 'Mauritius', 'Mauritius', 'MU'],
    ['647', 'Madagascar', 'Madagascar', 'MG'],
    ['649', 'Mali', 'Mali', 'ML'],
    ['650', 'Mozambique', 'Mozambique', 'MZ'],
    ['654', 'Mauritania', 'Mauritania', 'MR'],
    ['655', 'Malawi', 'Malawi', 'MW'],
    ['656', 'Niger', 'Niger', 'NE'],
    ['657', 'Nigeria', 'Nigeria', 'NG'],
    ['659', 'Namibia', 'Namibia', 'NA'],
    ['660', 'Reunion', 'Reunion', 'RE'],
    ['661', 'Rwanda', 'Rwanda', 'RW'],
    ['662', 'Sudan', 'Sudan', 'SD'],
    ['663', 'Senegal', 'Senegal', 'SN'],
    ['664', 'Seychelles', 'Seychelles', 'SC'],
    ['665', 'St Helena', 'St Helena', 'SH'],
    ['666', 'Somalia', 'Somalia', 'SO'],
    ['667', 'Sierra Leone', 'Sierra Leone', 'SL'],
    ['668', 'Sao Tome Principe', 'Sao Tome Principe', 'ST'],
    ['669', 'Swaziland', 'Swaziland', 'SZ'],
    ['670', 'Chad', 'Chad', 'TD'],
    ['671', 'Togo', 'Togo', 'TG'],
    ['672', 'Tunisia', 'Tunisia', 'TN'],
    ['674', 'Tanzania', 'Tanzania', 'TZ'],
    ['675', 'Uganda', 'Uganda', 'UG'],
    ['676', 'DR Congo', 'DR Congo', 'CD'],
    ['677', 'Tanzania', 'Tanzania', 'TZ'],
    ['678', 'Zambia', 'Zambia', 'ZM'],
    ['679', 'Zimbabwe', 'Zimbabwe', 'ZW'],
    ['701', 'Argentina', 'Argentina', 'AR'],
    ['710', 'Brazil', 'Brazil', 'BR'],
    ['720', 'Bolivia', 'Bolivia', 'BO'],
    ['725', 'Chile', 'Chile', 'CL'],
    ['730', 'Colombia', 'Colombia', 'CO'],
    ['735', 'Ecuador', 'Ecuador', 'EC'],
    ['740', 'UK', 'UK', 'UK'],
    ['745', 'Guiana', 'Guiana', 'GF'],
    ['750', 'Guyana', 'Guyana', 'GY'],
    ['755', 'Paraguay', 'Paraguay', 'PY'],
    ['760', 'Peru', 'Peru', 'PE'],
    ['765', 'Suriname', 'Suriname', 'SR'],
    ['770', 'Uruguay', 'Uruguay', 'UY'],
    ['775', 'Venezuela', 'Venezuela', 'VE']
] as const;

export const VESSEL_FLAG_CODE_DEFINITIONS: Record<string, VesselFlagCodeDefinition> = Object.fromEntries(
    map.map(([code, label, description, icon]) => [code, { code, label, description, icon }])
);

export const DEFAULT_VESSEL_FLAG_CODE: VesselFlagCodeDefinition = {
    code: 'DEFAULT',
    label: 'Unknown',
    description: 'Flag information not available',
    icon: 'DEFAULT',
};

const normalizeCode = (code?: string | number) => {
    if (code == null) return undefined;
    const key = typeof code === 'number' ? String(code) : String(code).trim();
    if (!key) return undefined;
    const normalized = key.length >= 3 ? key.slice(0, 3) : key.padStart(3, '0');
    return normalized;
};

export const getVesselFlagCodeDefinition = (code?: string | number) => {
    const normalized = normalizeCode(code);
    if (!normalized) return DEFAULT_VESSEL_FLAG_CODE;
    return VESSEL_FLAG_CODE_DEFINITIONS[normalized] ?? DEFAULT_VESSEL_FLAG_CODE;
};

export const getVesselFlagCodeFromMMSI = (mmsi?: string | number) => {
    if (mmsi == null) return DEFAULT_VESSEL_FLAG_CODE;
    const key = typeof mmsi === 'number' ? String(mmsi) : String(mmsi).trim();
    if (!key) return DEFAULT_VESSEL_FLAG_CODE;
    return getVesselFlagCodeDefinition(key.slice(0, 3));
};

