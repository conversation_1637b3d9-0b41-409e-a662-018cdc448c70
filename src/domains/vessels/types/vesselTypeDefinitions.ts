export interface VesselTypeDefinition {
  code: string;
  label: string;
  description: string;
  icon: string;
  category: VesselCategory;
  colorClass: string;
}

export const VESSEL_CATEGORIES = [
  'cargo',
  'tanker',
  'passenger',
  'fishing',
  'tug',
  'sailing',
  'pleasure',
  'highspeed',
  'military',
  'law',
  'medical',
  'dredging',
  'anti',
  'diving',
  'pilot',
  'porttender',
  'unspecified',
] as const;

export type VesselCategory = typeof VESSEL_CATEGORIES[number];

const map = <const>([
  ['0', 'Not available', 'Default vessel type when not specified', 'default.png', 'unspecified', 'bg-gray-500'],
  ['1', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['2', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['3', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['10', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['11', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['12', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['13', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['13', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['14', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['15', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['16', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['17', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['18', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['19', 'Reserved for future use', 'Reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['20', 'Wing in ground (WIG), all ships', 'Wing in ground (WIG), all ships of this type', 'wig.png', 'unspecified', 'bg-gray-500'],
  ['21', 'Wing in ground (WIG), Hazardous A', 'Wing in ground (WIG), Hazardous category A', 'wig.png', 'unspecified', 'bg-gray-500'],
  ['22', 'Wing in ground (WIG), Hazardous B', 'Wing in ground (WIG), Hazardous category B', 'wig.png', 'unspecified', 'bg-gray-500'],
  ['23', 'Wing in ground (WIG), Hazardous C', 'Wing in ground (WIG), Hazardous category C', 'wig.png', 'unspecified', 'bg-gray-500'],
  ['24', 'Wing in ground (WIG), Hazardous D', 'Wing in ground (WIG), Hazardous category D', 'wig.png', 'unspecified', 'bg-gray-500'],
  ['25', 'Wing in ground (WIG), Reserved', 'Wing in ground (WIG), Reserved for future use', 'wig.png', 'unspecified', 'bg-gray-500'],
  ['26', 'Wing in ground (WIG), Reserved', 'Wing in ground (WIG), Reserved for future use', 'wig.png', 'unspecified', 'bg-gray-500'],
  ['27', 'Wing in ground (WIG), Reserved', 'Wing in ground (WIG), Reserved for future use', 'wig.png', 'unspecified', 'bg-gray-500'],
  ['28', 'Wing in ground (WIG), Reserved', 'Wing in ground (WIG), Reserved for future use', 'wig.png', 'unspecified', 'bg-gray-500'],
  ['29', 'Wing in ground (WIG), Reserved', 'Wing in ground (WIG), Reserved for future use', 'wig.png', 'unspecified', 'bg-gray-500'],
  ['30', 'Fishing', 'Fishing vessel', 'fishing.png', 'fishing', 'bg-orange-500'],
  ['31', 'Towing', 'Vessel engaged in towing', 'towing.png', 'tug', 'bg-cyan-500'],
  ['32', 'Towing (large)', 'Vessel engaged in towing (length > 200m)', 'towing.png', 'tug', 'bg-cyan-500'],
  ['33', 'Dredging', 'Vessel engaged in dredging or underwater operations', 'dredging.png', 'dredging', 'bg-gray-500'],
  ['34', 'Diving', 'Vessel engaged in diving operations', 'diving.png', 'diving', 'bg-gray-500'],
  ['35', 'Military', 'Military operations vessel', 'military.png', 'military', 'bg-gray-500'],
  ['36', 'Sailing', 'Sailing vessel', 'sailing.png', 'sailing', 'bg-gray-500'],
  ['37', 'Pleasure craft', 'Pleasure craft', 'pleasure.png', 'pleasure', 'bg-pink-500'],
  ['40', 'High speed craft', 'High speed craft (HSC)', 'highspeedcraft.png', 'highspeed', 'bg-yellow-500'],
  ['50', 'Pilot vessel', 'Pilot vessel', 'pilot.png', 'pilot', 'bg-cyan-500'],
  ['51', 'Search and rescue', 'Search and rescue vessel', 'rescue.png', 'unspecified', 'bg-gray-500'],
  ['52', 'Tug', 'Tug boat', 'tugs.png', 'tug', 'bg-cyan-500'],
  ['53', 'Port tender', 'Port tender', 'porttender.png', 'porttender', 'bg-gray-500'],
  ['54', 'Anti-pollution', 'Anti-pollution equipment', 'anti.png', 'anti', 'bg-gray-500'],
  ['55', 'Law enforcement', 'Law enforcement vessel', 'law.png', 'law', 'bg-gray-500'],
  ['58', 'Medical transport', 'Medical transport', 'medical.png', 'medical', 'bg-gray-500'],
  ['59', 'Non-combatant ship', 'Non-combatant ship (RR Resolution No. 18)', 'noncombat.png', 'unspecified', 'bg-gray-500'],
  ['60', 'Passenger', 'Passenger ship', 'passenger.png', 'passenger', 'bg-blue-500'],
  ['70', 'Cargo', 'Cargo ship', 'cargo.png', 'cargo', 'bg-green-500'],
  ['71', 'Cargo (hazardous A)', 'Cargo ship carrying hazardous goods category A', 'cargo.png', 'cargo', 'bg-green-500'],
  ['72', 'Cargo (hazardous B)', 'Cargo ship carrying hazardous goods category B', 'cargo.png', 'cargo', 'bg-green-500'],
  ['73', 'Cargo (hazardous C)', 'Cargo ship carrying hazardous goods category C', 'cargo.png', 'cargo', 'bg-green-500'],
  ['74', 'Cargo (hazardous D)', 'Cargo ship carrying hazardous goods category D', 'cargo.png', 'cargo', 'bg-green-500'],
  ['80', 'Tanker', 'Tanker', 'tanker.png', 'tanker', 'bg-red-500'],
  ['81', 'Tanker (hazardous A)', 'Tanker carrying hazardous goods category A', 'tanker.png', 'tanker', 'bg-red-500'],
  ['82', 'Tanker (hazardous B)', 'Tanker carrying hazardous goods category B', 'tanker.png', 'tanker', 'bg-red-500'],
  ['83', 'Tanker (hazardous C)', 'Tanker carrying hazardous goods category C', 'tanker.png', 'tanker', 'bg-red-500'],
  ['84', 'Tanker (hazardous D)', 'Tanker carrying hazardous goods category D', 'tanker.png', 'tanker', 'bg-red-500'],
  ['90', 'Other', 'Other type of ship', 'default.png', 'unspecified', 'bg-gray-500'],
  ['91', 'Other (hazardous A)', 'Other type, hazardous category A', 'default.png', 'unspecified', 'bg-gray-500'],
  ['92', 'Other (hazardous B)', 'Other type, hazardous category B', 'default.png', 'unspecified', 'bg-gray-500'],
  ['93', 'Other (hazardous C)', 'Other type, hazardous category C', 'default.png', 'unspecified', 'bg-gray-500'],
  ['94', 'Other (hazardous D)', 'Other type, hazardous category D', 'default.png', 'unspecified', 'bg-gray-500'],
  ['95', 'Other (reserved)', 'Other type, reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['96', 'Other (reserved)', 'Other type, reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['97', 'Other (reserved)', 'Other type, reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['98', 'Other (reserved)', 'Other type, reserved for future use', 'default.png', 'unspecified', 'bg-gray-500'],
  ['99', 'Other (no additional information)', 'Other type, no additional information', 'default.png', 'unspecified', 'bg-gray-500'],
] satisfies readonly [string, string, string, string, VesselCategory][]);

export const VESSEL_TYPE_DEFINITIONS: Record<string, VesselTypeDefinition> = Object.fromEntries(
  map.map(([code, label, description, icon, category, colorClass]) => [code, { code, label, description, icon, category, colorClass }])
);

export const getVesselTypeDefinition = (code?: string | number) => {
  if (code == null) return undefined;
  const key = typeof code === 'number' ? String(code) : String(code).trim();
  if (!key) return undefined;
  return VESSEL_TYPE_DEFINITIONS[key];
};
