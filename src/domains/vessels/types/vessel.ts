export interface VesselInfo {
  mmsi: string;
  name: string;
  lat: number;
  lon: number;
  cog: number; // Course over ground
  sog: number; // Speed in knots
  hdg?: number; // Heading in degrees
  timestamp: string;
  aisType?: number;
  vesselType?: string;
  vesselTypeCode?: string;
  vesselTypeLabel?: string;
  vesselTypeIcon?: string;
  navStatus?: string;
  length?: number | null;
  width?: number | null;
  vesselClass?: string | null;
  callsign?: string | null;
  flagCode?: string;
  flagLabel?: string;
  flagIcon?: string;
}

export interface VesselMarker {
  marker?: maplibregl.Marker;
  info: VesselInfo;
}

export interface AISData {
  mmsi?: string;
  immsi?: string;
  userid?: string;
  lat: number;
  lon: number;
  cog?: number;
  sog?: number;
  hdg?: number;
  aistype?: number;
  shipname?: string;
  txt?: string;
  name?: string;
  callsign?: string;
}

export interface SocketMessage {
  data?: {
    data?: {
      message?: string;
      lat?: number;
      lon?: number;
      [key: string]: any;
    };
    [key: string]: any;
  };
  [key: string]: any;
}

export interface ConnectionStatus {
  connected: boolean;
  message: string;
}
