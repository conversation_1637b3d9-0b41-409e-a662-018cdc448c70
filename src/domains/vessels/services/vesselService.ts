import { request } from '@/shared/api/apiClient';
import type { VesselListResponse, VesselSummary, VesselApiResponse, VesselLogsResponse, VesselLogEntry, VesselLogPagination } from '@/shared/types/api';

const DEFAULT_LIMIT = 100;

const toNumber = (value: unknown): number | null => {
  if (value == null) return null;
  if (typeof value === 'number') {
    return Number.isFinite(value) ? value : null;
  }
  if (typeof value === 'string' && value.trim().length > 0) {
    const parsed = Number.parseFloat(value);
    return Number.isFinite(parsed) ? parsed : null;
  }
  return null;
};

const normalizeVessel = (input: VesselApiResponse): VesselSummary => ({
  mmsi: input.mmsi,
  shipname: input.shipname,
  imo: input.imo ?? null,
  callsign: input.callsign ?? null,
  vesselClass: input.class ?? '',
  vesselType: input.vesselType ?? null,
  vesselIcon: input.vesselIcon ?? null,
  length: toNumber(input.length),
  width: toNumber(input.width),
  lastTimestamp: input.lastTimestamp ?? null,
  lastLat: toNumber(input.lastLat),
  lastLon: toNumber(input.lastLon),
  lastSog: toNumber(input.lastSog),
  lastCog: toNumber(input.lastCog),
  lastHdg: toNumber(input.lastHdg),
  lastNavstatus: input.lastNavstatus ?? null,
});

export interface FetchRecentVesselsParams {
  lastSeenAfter: string;
  hasRecentPosition?: boolean;
  limit?: number;
  signal?: AbortSignal;
}

export interface FetchRecentVesselsResult {
  vessels: VesselSummary[];
  total: number;
}

export interface FetchVesselLogsParams {
  mmsi: string;
  from?: string;
  to?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  signal?: AbortSignal;
}

export interface FetchVesselLogsResult {
  logs: VesselLogEntry[];
  pagination: VesselLogPagination;
  mmsi: string;
  dateRange: {
    from: string;
    to: string;
  };
}

export const vesselService = {
  async fetchRecentVessels({
    lastSeenAfter,
    hasRecentPosition = true,
    limit = DEFAULT_LIMIT,
    signal,
  }: FetchRecentVesselsParams): Promise<FetchRecentVesselsResult> {
    const aggregated = new Map<string, VesselSummary>();
    let page = 1;
    let total = 0;

    while (true) {
      const response = await request<VesselListResponse>('/vessels', {
        method: 'GET',
        query: {
          page,
          limit,
          hasRecentPosition,
          lastSeenAfter,
        },
        signal,
      });

      const { vessels, total: remoteTotal } = response.data;
      total = remoteTotal;

      vessels.forEach((vessel) => {
        aggregated.set(vessel.mmsi, normalizeVessel(vessel));
      });

      const fetchedCount = aggregated.size;
      const receivedCount = vessels.length;

      const shouldStop =
        fetchedCount >= total ||
        receivedCount === 0 ||
        receivedCount < limit ||
        page * limit >= total;

      if (shouldStop) {
        break;
      }

      page += 1;
    }

    return {
      vessels: Array.from(aggregated.values()),
      total,
    };
  },

  async fetchVesselLogs({
    mmsi,
    from,
    to,
    page = 1,
    limit = 500,
    sortBy = 'timestamp',
    sortOrder = 'DESC',
    signal,
  }: FetchVesselLogsParams): Promise<FetchVesselLogsResult> {
    // Default to last 24 hours if no date range provided
    const now = new Date();
    const defaultTo = to || now.toISOString();
    const defaultFrom = from || new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();

    const response = await request<VesselLogsResponse>(`/ais/vessel/${mmsi}/logs`, {
      method: 'GET',
      query: {
        page,
        limit,
        sortBy,
        sortOrder,
        from: defaultFrom,
        to: defaultTo,
      },
      signal,
    });

    const { logs, pagination, dateRange } = response.data;

    return {
      logs,
      pagination,
      mmsi: response.data.mmsi,
      dateRange,
    };
  },
};
