import { useCallback, useEffect, useRef, useMemo, useState } from 'react';
import maplibregl from 'maplibre-gl';
import { formatCog, formatSog } from '@/shared/utils/formatters';
import { useVesselStore } from '../store/useVesselStore';
import { useFilters } from '@/shared/lib/FiltersContext';
import { VESSEL_TYPE_DEFINITIONS } from '../types/vesselTypeDefinitions';
import type { VesselInfo } from '../types/vessel';
import type { VesselLogEntry } from '@/shared/types/api';
import useLayerManager from '@/shared/hooks/useLayerManager';

// Exported layer identifiers for external controls (e.g., layer toggles)
export const VESSEL_LAYER_IDS = [
  'vessels-icons',
  'vessels-cog',
  'vessels-selection',
  'vessels-heatmap-layer',
  'vessel-track-line',
  'vessel-track-points',
];

export const useVesselMapLayers = (map: maplibregl.Map | null) => {
  const { filters } = useFilters();
  const vesselMap = useVesselStore((state) => state.vesselMap);
  const vesselTracks = useVesselStore((state) => state.vesselTracks);
  const selectedVesselId = useVesselStore((state) => state.selectedVesselId);
  const setSelectedVessel = useVesselStore((state) => state.setSelectedVessel);
  const clearSelectedVessel = useVesselStore((state) => state.clearSelectedVessel);
  // Playback state
  const isPlaybackMode = useVesselStore((state) => state.isPlaybackMode);
  const playbackVesselId = useVesselStore((state) => state.playbackVesselId);
  const playbackData = useVesselStore((state) => state.playbackData);
  const playbackCurrentIndex = useVesselStore((state) => state.playbackCurrentIndex);
  const vesselFeatureCollection = useVesselStore((state) => state.vesselFeatureCollection);
  const vesselHeatmapCollection = useVesselStore((state) => state.vesselHeatmapCollection);

  const layerManager = useLayerManager(map);
  const iconsLoadedRef = useRef<Set<string>>(new Set());
  const [layersReady, setLayersReady] = useState(false);
  const [trackLayersReady, setTrackLayersReady] = useState(false);
  const popupRef = useRef<maplibregl.Popup | null>(null);
  const hoverPopupRef = useRef<maplibregl.Popup | null>(null);
  const hoveredVesselRef = useRef<string | null>(null);
  const activeFeatureRef = useRef<maplibregl.MapGeoJSONFeature | null>(null);
  const [iconsLoadingComplete, setIconsLoadingComplete] = useState(false);

  const selectedVessel = useMemo(() => {
    // During playback, use the playback vessel as the selected vessel
    if (isPlaybackMode && playbackVesselId) {
      return vesselMap[playbackVesselId];
    }

    if (!selectedVesselId) return undefined;
    return vesselMap[selectedVesselId];
  }, [selectedVesselId, vesselMap, isPlaybackMode, playbackVesselId]);

  // Create track GeoJSON for the selected vessel
  const trackGeoJSON = useMemo(() => {
    let logs: VesselLogEntry[] = [];
    let vesselId = selectedVesselId;

    // In playback mode, use playback data
    if (isPlaybackMode && playbackVesselId && playbackData.length > 0) {
      logs = playbackData;
      vesselId = playbackVesselId;
    } else if (selectedVesselId && vesselTracks[selectedVesselId]) {
      logs = vesselTracks[selectedVesselId];
    }

    if (!vesselId || logs.length === 0) {
      return {
        type: 'FeatureCollection' as const,
        features: []
      };
    }

    const coordinates = logs
      .filter(log => log.position && log.position.lat && log.position.lon)
      .map(log => [parseFloat(log.position.lon), parseFloat(log.position.lat)]);

    if (coordinates.length < 2) {
      return {
        type: 'FeatureCollection' as const,
        features: []
      };
    }

    return {
      type: 'FeatureCollection' as const,
      features: [
        {
          type: 'Feature' as const,
          geometry: {
            type: 'LineString' as const,
            coordinates
          },
          properties: {
            mmsi: vesselId
          }
        }
      ]
    };
  }, [selectedVesselId, vesselTracks, isPlaybackMode, playbackVesselId, playbackData]);

  const getOrCreatePopup = useCallback(() => {
    if (!popupRef.current) {
      popupRef.current = new maplibregl.Popup({
        closeButton: false,
        closeOnClick: false,
        offset: [0, -10]
      });
    }
    return popupRef.current;
  }, []);

  const getOrCreateHoverPopup = useCallback(() => {
    if (!hoverPopupRef.current) {
      hoverPopupRef.current = new maplibregl.Popup({
        closeButton: false,
        closeOnClick: false,
        offset: [0, -10]
      });
    }
    return hoverPopupRef.current;
  }, []);

  const createPopupHTML = useCallback((properties: Record<string, unknown>) => {
    const sog = typeof properties.sog === 'number' ? properties.sog : undefined;
    const cog = typeof properties.cog === 'number' ? properties.cog : undefined;
    const timestamp = typeof properties.timestamp === 'string' ? properties.timestamp : '';

    const sogLabel = formatSog(sog);
    const cogLabel = formatCog(cog);

    return `
        <div>
          <h4>${properties.name || ''}</h4>
          <p><strong>MMSI:</strong> ${properties.mmsi || ''} - <strong>${sogLabel}/${cogLabel}</strong></p>
          <p><strong>Cập nhật:</strong> ${timestamp ? new Date(timestamp).toLocaleString() : 'N/A'}</p>
        </div>
      `;
  }, []);

  const hideOverlay = useCallback(() => {
    if (!map) return;

    if (map.getLayer('vessels-selection')) {
      map.setFilter('vessels-selection', ['==', ['get', 'mmsi'], '']);
    }

    if (map.getLayer('vessels-cog')) {
      map.setLayoutProperty('vessels-cog', 'visibility', 'none');
    }

    if (popupRef.current) {
      popupRef.current.remove();
    }
  }, [map]);

  const hideHoverOverlay = useCallback(() => {
    if (!map) return;

    if (hoverPopupRef.current) {
      hoverPopupRef.current.remove();
    }
  }, [map]);

  const showFeatureOverlay = useCallback((feature: maplibregl.MapGeoJSONFeature) => {
    if (!map) return;

    const props = feature.properties as Record<string, unknown> | undefined;
    const mmsi = props?.mmsi as string | undefined;
    if (!mmsi) {
      return;
    }

    if (map.getLayer('vessels-selection')) {
      map.setFilter('vessels-selection', ['==', ['get', 'mmsi'], mmsi]);
    }

    const cogSource = map.getSource('vessels-cog-source') as maplibregl.GeoJSONSource | null;
    const isAnchor = Boolean(props?.isAnchor);
    const cogValue = typeof props?.cog === 'number' ? (props.cog as number) : undefined;

    if (cogSource && map.getLayer('vessels-cog')) {
      if (!isAnchor && typeof cogValue === 'number' && cogValue > -1) {
        cogSource.setData({
          type: 'FeatureCollection',
          features: [feature]
        });
        map.setLayoutProperty('vessels-cog', 'visibility', 'visible');
      } else {
        map.setLayoutProperty('vessels-cog', 'visibility', 'none');
      }
    }

    const coordinates =
      feature.geometry.type === 'Point'
        ? (feature.geometry.coordinates as [number, number])
        : undefined;

    const popup = getOrCreatePopup();
    if (popup && coordinates) {
      popup
        .setLngLat([coordinates[0], coordinates[1]])
        .setHTML(createPopupHTML(props ?? {}))
        .addTo(map);
    }
  }, [map, createPopupHTML, getOrCreatePopup]);

  const showHoverPopup = useCallback((feature: maplibregl.MapGeoJSONFeature) => {
    if (!map) return;

    const props = feature.properties as Record<string, unknown> | undefined;
    const coordinates =
      feature.geometry.type === 'Point'
        ? (feature.geometry.coordinates as [number, number])
        : undefined;

    const hoverPopup = getOrCreateHoverPopup();
    if (hoverPopup && coordinates) {
      hoverPopup
        .setLngLat([coordinates[0], coordinates[1]])
        .setHTML(createPopupHTML(props ?? {}))
        .addTo(map);
    }
  }, [map, createPopupHTML, getOrCreateHoverPopup]);

  // Get all unique vessel icons needed
  const vesselIcons = useMemo(() => {
    const icons = new Set<string>();

    // Add all icons from vessel type definitions
    Object.values(VESSEL_TYPE_DEFINITIONS).forEach(def => {
      icons.add(def.icon);
      // Add anchor variant
      const dotIndex = def.icon.lastIndexOf('.');
      const anchorIcon = dotIndex === -1
        ? `${def.icon}_anchor`
        : `${def.icon.slice(0, dotIndex)}_anchor${def.icon.slice(dotIndex)}`;
      icons.add(anchorIcon);
    });

    // Add default icons
    icons.add('default.png');
    icons.add('default_anchor.png');

    return Array.from(icons);
  }, []);

  const getIconId = useCallback((info: VesselInfo) => {
    const baseIcon = info.vesselTypeIcon?.trim() || 'default.png';

    const withSuffix = (icon: string, suffix: string) => {
      const dotIndex = icon.lastIndexOf('.');
      if (dotIndex === -1) return `${icon}${suffix}`;
      return `${icon.slice(0, dotIndex)}${suffix}${icon.slice(dotIndex)}`;
    };

    const iconName = info.navStatus === 'at_anchor'
      ? withSuffix(baseIcon, '_anchor')
      : baseIcon;

    // Remove .png extension for icon ID
    return iconName.replace('.png', '');
  }, []);


  // Load all vessel icons into the map with proper sequencing
  useEffect(() => {
    if (!map || !layerManager) return;

    const loadIcons = async () => {
      const loadPromises = vesselIcons.map(async (iconFile) => {
        const iconId = iconFile.replace('.png', '');

        if (iconsLoadedRef.current.has(iconId)) {
          return Promise.resolve();
        }

        try {
          const image = await map.loadImage(`/assets/vessels/${iconFile}`);
          layerManager.addImage(iconId, image.data);
          iconsLoadedRef.current.add(iconId);
        } catch (error) {
          console.warn(`Failed to load vessel icon: ${iconFile}`, error);

          // Try to load fallback icon for this specific icon
          if (iconFile !== 'default.png') {
            try {
              const fallbackImage = await map.loadImage('/assets/vessels/default.png');
              layerManager.addImage('default', fallbackImage.data);
              iconsLoadedRef.current.add('default');

              layerManager.addImage(iconId, fallbackImage.data);
              iconsLoadedRef.current.add(iconId);
            } catch (fallbackError) {
              console.error(`Failed to load fallback icon for ${iconFile}:`, fallbackError);
            }
          }
        }
      });

      try {
        await Promise.all(loadPromises);
        setIconsLoadingComplete(true);
      } catch (error) {
        console.error('Error loading vessel icons:', error);
        setIconsLoadingComplete(true); // Still proceed even if some icons failed
      }
    };

    if (map.isStyleLoaded()) {
      loadIcons();
    } else {
      map.on('style.load', loadIcons);
    }
    return () => {
      map.off('style.load', loadIcons);
    };
  }, [map, layerManager, vesselIcons]);

  // Add styleimagemissing event handler as fallback
  useEffect(() => {
    if (!map || !layerManager) return;

    const handleStyleImageMissing = (e: { id: string }) => {
      const { id } = e;
      
      // Only handle vessel icons, not AtoN icons
      const isAtonIcon = id.startsWith('real_') || id.startsWith('virtual_');
      if (isAtonIcon) {
        return; // Let AtoN map layers handle AtoN icons
      }
      
      // Only load vessel icons
      const iconFile = `${id}.png`;
      const iconPath = `/assets/vessels/${iconFile}`;
      
      map.loadImage(iconPath)
        .then(image => {
          layerManager.addImage(id, image.data);
          iconsLoadedRef.current.add(id);
        })
        .catch(error => {
          console.warn(`Failed to load missing vessel icon: ${id}`, error);
          if (map.hasImage('default')) {
            const defaultImage = map.getImage('default');
            if (defaultImage) {
              layerManager.addImage(id, defaultImage);
              iconsLoadedRef.current.add(id);
            }
          }
        });
    };

    map.on('styleimagemissing', handleStyleImageMissing);

    return () => {
      map.off('styleimagemissing', handleStyleImageMissing);
    };
  }, [map, layerManager]);

  // Add GeoJSON sources and layers once icons are ready
  useEffect(() => {
    if (!map || !layerManager || !iconsLoadingComplete) return;

    const ensureUtilityImages = () => {
      if (!map.hasImage('cog-indicator')) {
        const cogCanvas = document.createElement('canvas');
        cogCanvas.width = 2;
        cogCanvas.height = 30;
        const cogCtx = cogCanvas.getContext('2d');
        if (cogCtx) {
          cogCtx.strokeStyle = 'rgba(27,112,255,0.95)';
          cogCtx.lineWidth = 2;
          cogCtx.setLineDash([4, 4]);
          cogCtx.beginPath();
          cogCtx.moveTo(1, 0);
          cogCtx.lineTo(1, 30);
          cogCtx.stroke();

          const imageData = cogCtx.getImageData(0, 0, cogCanvas.width, cogCanvas.height);
          layerManager.addImage('cog-indicator', imageData);
        }
      }

      if (!map.hasImage('vessel-selection-border')) {
        const borderCanvas = document.createElement('canvas');
        borderCanvas.width = 40;
        borderCanvas.height = 40;
        const borderCtx = borderCanvas.getContext('2d');
        if (borderCtx) {
          borderCtx.strokeStyle = '#C93333';
          borderCtx.lineWidth = 3;
          borderCtx.setLineDash([4, 4]);
          borderCtx.strokeRect(2, 2, 36, 36);

          const imageData = borderCtx.getImageData(0, 0, borderCanvas.width, borderCanvas.height);
          layerManager.addImage('vessel-selection-border', imageData);
        }
      }
    };

    ensureUtilityImages();

    layerManager.ensureGeoJSONSource('vessels', { data: vesselFeatureCollection });

    layerManager.ensureLayer({
      id: 'vessels-icons',
      type: 'symbol',
      source: 'vessels',
      minzoom: 9,
      layout: {
        'icon-image': ['get', 'iconId'],
        'icon-size': [
          'case',
          ['get', 'isAnchor'],
          0.5,
          0.7
        ],
        'icon-rotation-alignment': 'map',
        'icon-pitch-alignment': 'map',
        'icon-rotate': ['get', 'rotation'],
        'icon-allow-overlap': true,
        'icon-ignore-placement': true
      },
      paint: {
        'icon-opacity': 1
      }
    });

    layerManager.ensureGeoJSONSource('vessels-cog-source', {
      data: {
        type: 'FeatureCollection',
        features: []
      }
    });

    layerManager.ensureLayer({
      id: 'vessels-cog',
      type: 'symbol',
      source: 'vessels-cog-source',
      layout: {
        'icon-image': 'cog-indicator',
        'icon-size': 1,
        'icon-rotation-alignment': 'map',
        'icon-pitch-alignment': 'map',
        'icon-rotate': ['get', 'cog'],
        'icon-allow-overlap': true,
        'icon-ignore-placement': true,
        'icon-offset': [0, -15],
        'visibility': 'none'
      },
      paint: {
        'icon-opacity': 0.8
      }
    });

    layerManager.ensureLayer({
      id: 'vessels-selection',
      type: 'symbol',
      source: 'vessels',
      filter: ['==', ['get', 'mmsi'], ''],
      layout: {
        'icon-image': 'vessel-selection-border',
        'icon-size': 0.7,
        'icon-allow-overlap': true,
        'icon-ignore-placement': true
      },
      paint: {
        'icon-opacity': 1
      }
    });

    layerManager.ensureGeoJSONSource('vessels-heatmap', { data: vesselHeatmapCollection });

    layerManager.ensureLayer({
      id: 'vessels-heatmap-layer',
      type: 'heatmap',
      source: 'vessels-heatmap',
      maxzoom: 9,
      paint: {
        'heatmap-weight': [
          'interpolate',
          ['linear'],
          ['get', 'weight'],
          0,
          0,
          6,
          1
        ],
        'heatmap-intensity': [
          'interpolate',
          ['linear'],
          ['zoom'],
          0,
          1,
          9,
          3
        ],
        'heatmap-color': [
          'interpolate',
          ['linear'],
          ['heatmap-density'],
          0,
          'rgba(33,102,172,0)',
          0.2,
          'rgb(103,169,207)',
          0.4,
          'rgb(209,229,240)',
          0.6,
          'rgb(253,219,199)',
          0.8,
          'rgb(239,138,98)',
          1,
          'rgb(178,24,43)'
        ],
        'heatmap-radius': [
          'interpolate',
          ['linear'],
          ['zoom'],
          0,
          2,
          9,
          20
        ],
        'heatmap-opacity': 1
      }
    });

    setLayersReady(true);
  }, [map, layerManager, vesselFeatureCollection, vesselHeatmapCollection, iconsLoadingComplete]);

  // Add track layers
  useEffect(() => {
    if (!map || !layerManager || !iconsLoadingComplete || trackLayersReady) return;

    layerManager.ensureGeoJSONSource('vessel-track', { data: trackGeoJSON });

    layerManager.ensureLayer({
      id: 'vessel-track-line',
      type: 'line',
      source: 'vessel-track',
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': '#eab308',
        'line-width': 2,
        'line-opacity': 0.8
      }
    });

    layerManager.ensureLayer({
      id: 'vessel-track-points',
      type: 'circle',
      source: 'vessel-track',
      filter: ['==', '$type', 'Point'],
      paint: {
        'circle-radius': 4,
        'circle-color': '#3b82f6',
        'circle-stroke-width': 2,
        'circle-stroke-color': '#ffffff'
      }
    });

    setTrackLayersReady(true);
  }, [map, layerManager, iconsLoadingComplete, trackLayersReady, trackGeoJSON]);

  // Update track data when it changes
  useEffect(() => {
    if (!map || !layerManager || !trackLayersReady) return;

    layerManager.updateGeoJSONSource('vessel-track', trackGeoJSON);
  }, [map, layerManager, trackGeoJSON, trackLayersReady]);

  // Update vessel data when it changes
  useEffect(() => {
    if (!map || !layerManager || !layersReady) return;

    layerManager.updateGeoJSONSource('vessels', vesselFeatureCollection);
  }, [map, layerManager, vesselFeatureCollection, layersReady]);

  // Update heatmap data when it changes
  useEffect(() => {
    if (!map || !layerManager || !layersReady) return;

    layerManager.updateGeoJSONSource('vessels-heatmap', vesselHeatmapCollection);
  }, [map, layerManager, vesselHeatmapCollection, layersReady]);

  const vesselFilterExpression = useMemo<maplibregl.FilterSpecification | null>(() => {
    const clauses: unknown[] = ['all'];
    if (isPlaybackMode && playbackVesselId && playbackData.length > 0) {
      clauses.push(['==', ['get', 'mmsi'], playbackVesselId]);
    }
    if (filters.vesselTypes.length > 0) {
      clauses.push(['in', ['get', 'vesselType'], ['literal', filters.vesselTypes]]);
    }
    if (filters.vesselNavStatuses.length > 0) {
      clauses.push(['in', ['get', 'navStatus'], ['literal', filters.vesselNavStatuses]]);
    }
    return clauses.length > 1 ? (clauses as maplibregl.FilterSpecification) : null;
  }, [filters, isPlaybackMode, playbackData, playbackVesselId]);

  useEffect(() => {
    if (!map || !layersReady) return;

    map.setFilter('vessels-icons', vesselFilterExpression);
    map.setFilter('vessels-heatmap-layer', vesselFilterExpression);
  }, [map, layersReady, vesselFilterExpression]);

  // Add interactivity for popups and hover effects
  useEffect(() => {
    if (!map || !layerManager || !layersReady) return;

    const onMouseEnter = (e: maplibregl.MapLayerMouseEvent & { features?: maplibregl.MapGeoJSONFeature[] }) => {
      try {
        if (!e.features || !e.features[0]) return;

        const feature = e.features[0];
        hoveredVesselRef.current = feature.properties?.mmsi as string | null;
        map.getCanvas().style.cursor = 'pointer';

        if (selectedVessel) {
          showHoverPopup(feature);
        } else {
          showFeatureOverlay(feature);
        }
      } catch (error) {
        console.error('Error in onMouseEnter:', error);
      }
    };

    const onMouseLeave = () => {
      try {
        map.getCanvas().style.cursor = '';
        hoveredVesselRef.current = null;
        hideHoverOverlay();
        if (!selectedVessel) {
          hideOverlay();
        }
      } catch (error) {
        console.error('Error in onMouseLeave:', error);
      }
    };

    const onClick = (e: maplibregl.MapLayerMouseEvent & { features?: maplibregl.MapGeoJSONFeature[] }) => {
      try {
        if (!e.features || !e.features[0]) return;

        const feature = e.features[0];
        const mmsi = feature.properties?.mmsi as string | undefined;
        if (!mmsi) return;

        setSelectedVessel(mmsi);
        activeFeatureRef.current = feature;
        showFeatureOverlay(feature);
      } catch (error) {
        console.error('Error in onClick:', error);
      }
    };

    const offMouseEnter = layerManager.bindLayerEvent('vessels-icons', 'mouseenter', onMouseEnter);
    const offMouseLeave = layerManager.bindLayerEvent('vessels-icons', 'mouseleave', onMouseLeave);
    const offLayerClick = layerManager.bindLayerEvent('vessels-icons', 'click', onClick);

    const onMapClick = (event: maplibregl.MapMouseEvent) => {
      const features = map.queryRenderedFeatures(event.point, { layers: ['vessels-icons'] });
      if (features.length > 0) {
        return;
      }
      clearSelectedVessel();
      activeFeatureRef.current = null;
      hideOverlay();
    };

    const offMapClick = layerManager.bindMapEvent('click', onMapClick);

    return () => {
      offMouseEnter();
      offMouseLeave();
      offLayerClick();
      offMapClick();
      hideHoverOverlay();
    };
  }, [map, layerManager, layersReady, selectedVessel, clearSelectedVessel, hideOverlay, hideHoverOverlay, setSelectedVessel, showFeatureOverlay, showHoverPopup]);

  useEffect(() => {
    if (!map) return;
    if (!selectedVessel) {
      activeFeatureRef.current = null;
      if (!hoveredVesselRef.current) {
        hideOverlay();
      }
      return;
    }

    if (!Number.isFinite(selectedVessel.lon) || !Number.isFinite(selectedVessel.lat)) {
      clearSelectedVessel();
      return;
    }

    // Get current playback data if in playback mode
    let currentSog = selectedVessel.sog ?? 0;
    let currentCog = selectedVessel.cog ?? 0;
    let currentHdg = selectedVessel.hdg ?? 0;
    let currentTimestamp = selectedVessel.timestamp || '';

    if (isPlaybackMode && playbackVesselId === selectedVessel.mmsi && playbackData.length > 0) {
      // Use the current playback index to get the corresponding log entry
      const currentLog = playbackData[playbackCurrentIndex] || playbackData[0];

      if (currentLog && currentLog.position) {
        currentSog = parseFloat(currentLog.position.sog) || 0;
        currentCog = parseFloat(currentLog.position.cog) || 0;
        currentHdg = currentLog.position.hdg || 0;
        currentTimestamp = currentLog.timestamp;
      }
    }

    const feature = {
      type: 'Feature',
      id: selectedVessel.mmsi,
      geometry: {
        type: 'Point',
        coordinates: [selectedVessel.lon, selectedVessel.lat],
      },
      properties: {
        mmsi: selectedVessel.mmsi,
        name: selectedVessel.name || '',
        vesselType: selectedVessel.vesselType || '',
        vesselTypeIcon: selectedVessel.vesselTypeIcon || 'default.png',
        navStatus: selectedVessel.navStatus || '',
        hdg: currentHdg,
        cog: currentCog,
        sog: currentSog,
        timestamp: currentTimestamp,
        lat: selectedVessel.lat,
        lon: selectedVessel.lon,
        iconId: getIconId(selectedVessel),
        isAnchor: selectedVessel.navStatus === 'at_anchor',
        showVessel: true,
      },
    } as unknown as maplibregl.MapGeoJSONFeature;

    activeFeatureRef.current = feature;

    if (!hoveredVesselRef.current || hoveredVesselRef.current !== selectedVessel.mmsi) {
      showFeatureOverlay(feature);
    }
  }, [map, selectedVessel, clearSelectedVessel, hideOverlay, showFeatureOverlay, getIconId, isPlaybackMode, playbackVesselId, playbackData, playbackCurrentIndex]);

  useEffect(() => () => {
    popupRef.current?.remove();
    hoverPopupRef.current?.remove();
    iconsLoadedRef.current.clear();
  }, []);

  useEffect(() => {
    if (!map) {
      iconsLoadedRef.current.clear();
      setIconsLoadingComplete(false);
      setLayersReady(false);
      setTrackLayersReady(false);
    }
  }, [map]);

  // Auto-center map on playback vessel when playback starts
  useEffect(() => {
    if (!map || !isPlaybackMode || !playbackVesselId || playbackData.length === 0) return;

    // Only center on the first position when playback starts, not on every frame
    const currentLog = playbackData[0]; // Always use the first log entry
    if (currentLog && currentLog.position) {
      const lat = parseFloat(currentLog.position.lat);
      const lon = parseFloat(currentLog.position.lon);

      if (Number.isFinite(lat) && Number.isFinite(lon)) {
        // Center on the starting position without animation to avoid jarring movement
        map.jumpTo({ center: [lon, lat] });
      }
    }
  }, [map, isPlaybackMode, playbackVesselId, playbackData]);

  return {
    selectedVessel,
    trackGeoJSON,
  };
};
