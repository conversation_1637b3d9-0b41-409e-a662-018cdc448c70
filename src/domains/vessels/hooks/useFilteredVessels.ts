import { useMemo } from 'react';
import { useFilters, type FiltersState } from '@/shared/lib/FiltersContext';
import { useVesselStore } from '../store/useVesselStore';
import type { VesselInfo } from '../types/vessel';

const matchesFilters = (vessel: VesselInfo, filters: FiltersState) => {
  const byType =
    filters.vesselTypes.length === 0 || (vessel.vesselType ? filters.vesselTypes.includes(vessel.vesselType) : false);
  const byNav =
    filters.vesselNavStatuses.length === 0 || (vessel.navStatus ? filters.vesselNavStatuses.includes(vessel.navStatus) : false);
  return byType && byNav;
};

export function useFilteredVessels(): VesselInfo[] {
  const { filters } = useFilters();
  const vesselList = useVesselStore((state) => state.vesselList);

  return useMemo(() => vesselList.filter((vessel) => matchesFilters(vessel, filters)), [vesselList, filters]);
}

export function useVesselFilterPredicate() {
  const { filters } = useFilters();
  return (vessel: VesselInfo) => matchesFilters(vessel, filters);
}
