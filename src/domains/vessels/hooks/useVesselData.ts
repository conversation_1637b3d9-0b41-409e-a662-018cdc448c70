import { useEffect } from 'react';
import { useFilters } from '@/shared/lib/FiltersContext';
import { useVesselStore } from '../store/useVesselStore';

export const useVesselData = () => {
  const { filters } = useFilters();

  const vesselList = useVesselStore((state) => state.vesselList);
  const vesselMap = useVesselStore((state) => state.vesselMap);
  const vesselTracks = useVesselStore((state) => state.vesselTracks);
  const connectionStatus = useVesselStore((state) => state.connectionStatus);
  const fetchRecentVessels = useVesselStore((state) => state.fetchRecentVessels);
  const lastFetchedAt = useVesselStore((state) => state.lastFetchedAt);
  const isLoading = useVesselStore((state) => state.isLoading);
  const startSocket = useVesselStore((state) => state.startSocket);
  const stopSocket = useVesselStore((state) => state.stopSocket);
  const selectedVesselId = useVesselStore((state) => state.selectedVesselId);
  const setSelectedVessel = useVesselStore((state) => state.setSelectedVessel);
  const clearSelectedVessel = useVesselStore((state) => state.clearSelectedVessel);
  // Playback state
  const isPlaybackMode = useVesselStore((state) => state.isPlaybackMode);
  const playbackVesselId = useVesselStore((state) => state.playbackVesselId);
  const playbackData = useVesselStore((state) => state.playbackData);
  const playbackCurrentIndex = useVesselStore((state) => state.playbackCurrentIndex);

  // Initialize data fetching and socket connection
  useEffect(() => {
    if (!lastFetchedAt && !isLoading) {
      fetchRecentVessels().catch((error) => {
        console.error('Failed to load recent vessels:', error);
      });
    }
  }, [fetchRecentVessels, lastFetchedAt, isLoading]);

  useEffect(() => {
    startSocket();
    return () => {
      stopSocket();
    };
  }, [startSocket, stopSocket]);

  return {
    vesselList,
    vesselMap,
    vesselTracks,
    connectionStatus,
    selectedVesselId,
    setSelectedVessel,
    clearSelectedVessel,
    isPlaybackMode,
    playbackVesselId,
    playbackData,
    playbackCurrentIndex,
    filters,
  };
};
