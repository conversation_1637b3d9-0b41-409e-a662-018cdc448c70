export interface AreaGeometry {
  type: 'Polygon' | 'MultiPolygon';
  coordinates: number[][][] | number[][][][];
}

export interface Area {
  id: string;
  name: string;
  kind: string | null;
  geom: AreaGeometry;
  bufferm: number | null;
  active: boolean;
  metadata: unknown | null;
  schedule: unknown | null;
  createdAt: string;
  updatedAt: string;
  rules: unknown[];
  alerts: unknown[];
}

export interface AreasResponse {
  success: boolean;
  message: string;
  data: Area[];
  total: number;
  page: string;
  limit: string;
  timestamp: string;
  statusCode: number;
}

export interface FetchAreasParams {
  page?: number;
  limit?: number;
  active?: boolean;
  signal?: AbortSignal;
}

