import { useEffect, useRef } from 'react';
import type maplibregl from 'maplibre-gl';
import { areaService } from '../services/areaService';
import type { Area } from '../types/area';
import useLayerManager from '@/shared/hooks/useLayerManager';

const AREAS_SOURCE_ID = 'areas';
export const AREAS_LAYER_ID = 'areas-outline';

function toFeature(area: Area): GeoJSON.Feature<GeoJSON.Polygon | GeoJSON.MultiPolygon> {
    const geometry = area.geom as unknown as GeoJSON.Polygon | GeoJSON.MultiPolygon;
    return {
        type: 'Feature',
        properties: {
            id: area.id,
            name: area.name,
        },
        geometry,
    };
}

export function useAreasLayer(map: maplibregl.Map | null) {
    const abortRef = useRef<AbortController | null>(null);
    const layerManager = useLayerManager(map);

    useEffect(() => {
        if (!map || !layerManager) return;

        let isMounted = true;

        const ensureLayers = (featureCollection: GeoJSON.FeatureCollection) => {
            layerManager.ensureGeoJSONSource(AREAS_SOURCE_ID, { data: featureCollection });
            layerManager.ensureLayer({
                id: AREAS_LAYER_ID,
                type: 'line',
                source: AREAS_SOURCE_ID,
                paint: {
                    'line-color': '#ff1a1a',
                    'line-width': 2,
                    'line-dasharray': [2, 2],
                },
            });
        };

        const fetchAndRender = async () => {
            try {
                abortRef.current?.abort();
                const controller = new AbortController();
                abortRef.current = controller;
                const areas = await areaService.fetchAreas({ page: 1, limit: 100, active: true, signal: controller.signal });
                if (!isMounted) return;
                const features = areas.map(toFeature);
                const featureCollection: GeoJSON.FeatureCollection = {
                    type: 'FeatureCollection',
                    features,
                };
                ensureLayers(featureCollection);
            } catch (err) {
                if ((err as unknown as { name?: string })?.name === 'AbortError') return;
                // Non-fatal: log and skip
                console.warn('Failed to load areas:', err);
            }
        };

        if (map.loaded()) {
            fetchAndRender();
        } else {
            const onLoad = () => fetchAndRender();
            map.once('load', onLoad);
            return () => {
                map.off('load', onLoad);
            };
        }

        return () => {
            isMounted = false;
            abortRef.current?.abort();
        };
    }, [map, layerManager]);
}
