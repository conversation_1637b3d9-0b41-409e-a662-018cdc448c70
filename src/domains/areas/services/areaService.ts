import { request } from '@/shared/api/apiClient';
import type { AreasResponse, FetchAreasParams } from '../types/area';

export const areaService = {
  async fetchAreas(params: FetchAreasParams = {}) {
    const { page = 1, limit = 100, active = true, signal } = params;
    const response = await request<AreasResponse>('/areas', {
      method: 'GET',
      query: { page, limit, active },
      signal,
    });
    return response.data;
  },
};

export type AreaService = typeof areaService;

