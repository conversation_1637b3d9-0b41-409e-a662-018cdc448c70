import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import English namespaces
import enCommon from './locales/en/common.json';
import enNavigation from './locales/en/navigation.json';
import enDashboard from './locales/en/dashboard.json';
import enDevice from './locales/en/device.json';
import enAlerts from './locales/en/alerts.json';
import enMap from './locales/en/map.json';
import enAuth from './locales/en/auth.json';
import enTimeline from './locales/en/timeline.json';

// Import Vietnamese namespaces
import viCommon from './locales/vi/common.json';
import viNavigation from './locales/vi/navigation.json';
import viDashboard from './locales/vi/dashboard.json';
import viDevice from './locales/vi/device.json';
import viAlerts from './locales/vi/alerts.json';
import viMap from './locales/vi/map.json';
import viAuth from './locales/vi/auth.json';
import viTimeline from './locales/vi/timeline.json';

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        common: enCommon,
        navigation: enNavigation,
        dashboard: enDashboard,
        device: enDevice,
        alerts: enAlerts,
        map: enMap,
        auth: enAuth,
        timeline: enTimeline,
      },
      vi: {
        common: viCommon,
        navigation: viNavigation,
        dashboard: viDashboard,
        device: viDevice,
        alerts: viAlerts,
        map: viMap,
        auth: viAuth,
        timeline: viTimeline,
      },
    },
    lng: 'vi', // Default language (Vietnamese)
    fallbackLng: 'vi',
    defaultNS: 'common', // Default namespace
    ns: ['common', 'navigation', 'dashboard', 'device', 'alerts', 'map', 'auth', 'timeline'],
    interpolation: {
      escapeValue: false, // React already escapes by default
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
  });

export default i18n;
