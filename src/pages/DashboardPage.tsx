import React from 'react';
import Sidebar from '../components/common/Sidebar';
import MapView from '../components/map/MapView';
import { useVesselData } from '../domains/vessels/hooks/useVesselData';
import Header from '../components/common/Header';
import PlaybackControls from '../components/map/PlaybackControls';
import { useTheme } from '../shared/lib/ThemeContext';
import { useAtonStore } from '../domains/atons/store/useAtonStore';
import { useFilteredVessels } from '../domains/vessels/hooks/useFilteredVessels';
import { useDashboardControls } from '../shared/hooks/useDashboardControls';
import RightPanel from '../components/panels/dashboard/RightPanel';

export type NavaidFilterType = 'all' | 'sealite' | 'lighthouse' | 'beacon' | 'marker';
export type StatusFilterType = 'all' | 'operational' | 'maintenance' | 'offline';

const Dashboard: React.FC = () => {
  const { theme } = useTheme();
  const controls = useDashboardControls();
  const { connectionStatus, vesselList } = useVesselData();
  const atonList = useAtonStore((state) => state.atonList);
  const filteredVesselList = useFilteredVessels();
  const themeName = theme === 'dark' ? 'dark' : 'light';

  return (
    <div
      ref={controls.appContainerRef}
      className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-100 text-gray-900'}`}
    >
      <div className="flex flex-col h-screen">
        <div className="flex flex-1 overflow-hidden">
          {!controls.isFocusMode && (
            <Sidebar
              isCollapsed={controls.isSidebarCollapsed}
              onToggleCollapse={controls.toggleSidebarCollapsed}
            />
          )}
          
          <div className="flex-1 flex flex-col overflow-hidden">
            <Header 
              selectedDate={controls.selectedDate}
              searchQuery={controls.searchQuery}
              setSearchQuery={controls.setSearchQuery}
              selectedFilter={'all'}
              setSelectedFilter={() => {}}
              selectedStatusFilter={'all'}
              setSelectedStatusFilter={() => {}}
              isFullscreen={controls.isFullscreen}
              onToggleFullscreen={controls.toggleFullscreen}
              isFocusMode={controls.isFocusMode}
              onToggleFocusMode={controls.toggleFocusMode}
              vessels={vesselList}
              onSelectVessel={controls.jumpToCoordinates}
            />
            
            <div className="flex-1 flex overflow-hidden">
            <div className="flex-1 flex flex-col overflow-hidden">
              <div className="flex-1 overflow-hidden">
                <MapView onMapReady={controls.handleMapReady} />
              </div>
                
                {/* Temporarily hide global playback control - will be used for all vessels playback later */}
                {/* {!isFocusMode && <TimelineControls />} */}
              </div>
              
              {!controls.isFocusMode && (
                <RightPanel
                  theme={themeName}
                  sidePanel={controls.sidePanel}
                  onSidePanelChange={controls.setSidePanel}
                  isCollapsed={controls.isRightPanelCollapsed}
                  onToggleCollapse={controls.setRightPanelCollapsed}
                  vessels={filteredVesselList}
                  connectionStatus={connectionStatus}
                  atons={atonList}
                  onSelectVessel={controls.jumpToCoordinates}
                />
              )}
              
            </div>
          </div>
        </div>
      </div>
      
      <PlaybackControls />
    </div>
  );
};

export default Dashboard;
