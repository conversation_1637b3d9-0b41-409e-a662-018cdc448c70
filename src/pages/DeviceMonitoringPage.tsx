import React, { useState } from 'react';
import Sidebar from '../components/common/Sidebar';
import Header from '../components/common/Header';
import { useTheme } from '../shared/lib/ThemeContext';
import { useTranslation } from 'react-i18next';

const DeviceMonitoringPage: React.FC = () => {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState<boolean>(false);

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-100 text-gray-900'}`}>
      <div className="flex flex-col h-screen">
        <div className="flex flex-1 overflow-hidden">
          <Sidebar 
            isCollapsed={isSidebarCollapsed}
            onToggleCollapse={() => setIsSidebarCollapsed((prev) => !prev)}
          />

          <div className="flex-1 flex flex-col overflow-hidden">
            <Header 
              selectedDate={''}
              searchQuery={''}
              setSearchQuery={() => {}}
              selectedFilter={'all' as unknown as 'all'}
              setSelectedFilter={() => {}}
              selectedStatusFilter={'all' as unknown as 'all'}
              setSelectedStatusFilter={() => {}}
              isFullscreen={false}
              onToggleFullscreen={() => {}}
              isFocusMode={false}
              onToggleFocusMode={() => {}}
              vessels={[]}
            />

            <div className="flex-1 overflow-y-auto p-6">
              <h1 className="text-2xl font-bold mb-4">{t('device:device.monitoring')}</h1>

              {/* KPIs */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <KpiCard title={t('dashboard:total.devices')} value="128" />
                <KpiCard title={t('dashboard:operational')} value="117" valueClass="text-green-400" />
                <KpiCard title={t('dashboard:maintenance')} value="7" valueClass="text-yellow-400" />
                <KpiCard title={t('dashboard:offline')} value="4" valueClass="text-red-400" />
              </div>

              {/* Devices table */}
              <div className="mt-6 bg-gray-800 rounded-lg p-4 overflow-auto">
                <div className="font-semibold mb-3">{t('dashboard:all.devices')}</div>

                <table className="min-w-full text-sm">
                  <thead>
                    <tr className="text-left text-gray-300">
                      <Th>Port</Th>
                      <Th>Name</Th>
                      <Th>Serial No</Th>
                      <Th>Position Status</Th>
                      <Th>Battery Voltages</Th>
                      <Th>Solar Charge</Th>
                      <Th>Load Amps</Th>
                      <Th>Flash Code</Th>
                      <Th>Light Status</Th>
                      <Th>Last Update Time</Th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                    {mockRows.map((r, idx) => (
                      <tr key={idx} className={idx % 2 === 0 ? 'bg-gray-900' : ''}>
                        <Td>{r.port}</Td>
                        <Td className="font-medium">{r.name}</Td>
                        <Td className="tabular-nums">{r.serial}</Td>
                        <Td>
                          <Badge color={r.position === 'OK' ? 'green' : 'red'}>{r.position}</Badge>
                        </Td>
                        <Td className="tabular-nums">{r.battery} volt</Td>
                        <Td className="tabular-nums">{r.solar} mA</Td>
                        <Td className="tabular-nums">{r.load ?? 'NA'}</Td>
                        <Td className="tabular-nums">{r.flash}</Td>
                        <Td>
                          <Badge color={r.light === 'ON' ? 'green' : 'gray'}>{r.light}</Badge>
                        </Td>
                        <Td>
                          <div className="flex flex-col">
                            <span className="tabular-nums text-gray-300">{r.lastTime}</span>
                            <span className="text-xs text-red-400">{r.lastAgo}</span>
                          </div>
                        </Td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceMonitoringPage;

/* helpers */
type BadgeColor = 'green' | 'red' | 'gray' | 'yellow' | 'blue';

const Badge: React.FC<{ color: BadgeColor; children: React.ReactNode }> = ({ color, children }) => (
  <span className={`px-2 py-0.5 text-xs rounded-full ${
    color === 'green' ? 'bg-green-500' :
    color === 'red' ? 'bg-red-500' :
    color === 'yellow' ? 'bg-yellow-500' :
    color === 'blue' ? 'bg-blue-500' : 'bg-gray-600'
  }`}>
    {children}
  </span>
);

const Th: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <th className="py-2 pr-4 whitespace-nowrap border-b border-gray-700">{children}</th>
);

const Td: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
  <td className={`py-3 pr-4 whitespace-nowrap ${className || ''}`}>{children}</td>
);

const KpiCard: React.FC<{ title: string; value: string; valueClass?: string }> = ({ title, value, valueClass }) => (
  <div className="p-4 rounded-lg bg-gray-800">
    <div className="text-sm text-gray-400">{title}</div>
    <div className={`text-2xl font-bold ${valueClass || ''}`}>{value}</div>
  </div>
);

const mockRows = [
  { port: 'Tân Thuận', name: 'DGCMTH3', serial: '0337915882', position: 'OFF', battery: '4.15', solar: '0', load: 'NA', flash: '075', light: 'ON', lastTime: '14/09/2025 18:03:01', lastAgo: '4d:21h:32m:38s' },
  { port: 'Tân Thuận', name: 'WT01 S1', serial: '0813392941', position: 'OK', battery: '3.7', solar: '0', load: 'NA', flash: '009', light: 'ON', lastTime: '19/09/2025 06:06:16', lastAgo: '9h:29m:23s' },
  { port: 'Tân Thuận', name: 'DGCMTH1', serial: '0338605890', position: 'OK', battery: '4.07', solar: '45', load: 'NA', flash: '075', light: 'ON', lastTime: '18/09/2025 18:00:33', lastAgo: '21h:35m:6s' },
  { port: 'Tân Thuận', name: 'DGCMTH2', serial: '0337912595', position: 'OK', battery: '4.09', solar: '9', load: 'NA', flash: '075', light: 'ON', lastTime: '14/09/2025 06:01:08', lastAgo: '5d:9h:34m:31s' },
  { port: 'Tân Thuận', name: 'WT12-S2', serial: '0813503941', position: 'OK', battery: '3.8', solar: '0', load: 'NA', flash: '009', light: 'ON', lastTime: '18/09/2025 18:32:48', lastAgo: '21h:2m:51s' },
  { port: 'Tân Thuận', name: 'WT11-S1', serial: '0813395941', position: 'OK', battery: '3.8', solar: '0', load: 'NA', flash: '009', light: 'ON', lastTime: '18/09/2025 18:11:27', lastAgo: '21h:24m:12s' },
  { port: 'Tân Thuận', name: 'WT24-S1', serial: '0813375941', position: 'OK', battery: '3.7', solar: '0', load: 'NA', flash: '009', light: 'ON', lastTime: '19/09/2025 06:02:27', lastAgo: '9h:33m:12s' },
];


