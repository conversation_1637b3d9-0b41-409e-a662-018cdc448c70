import React, { useState } from 'react';
import Sidebar from '../components/common/Sidebar';
import Header from '../components/common/Header';
import { useTheme } from '../shared/lib/ThemeContext';

const ReportPage: React.FC = () => {
  const { theme } = useTheme();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState<boolean>(false);

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-100 text-gray-900'}`}>
      <div className="flex flex-col h-screen">
        <div className="flex flex-1 overflow-hidden">
          <Sidebar 
            isCollapsed={isSidebarCollapsed}
            onToggleCollapse={() => setIsSidebarCollapsed((prev) => !prev)}
          />

          <div className="flex-1 flex flex-col overflow-hidden">
            <Header 
              selectedDate={''}
              searchQuery={''}
              setSearchQuery={() => {}}
              selectedFilter={'all' as unknown as 'all'}
              setSelectedFilter={() => {}}
              selectedStatusFilter={'all' as unknown as 'all'}
              setSelectedStatusFilter={() => {}}
              isFullscreen={false}
              onToggleFullscreen={() => {}}
              isFocusMode={false}
              onToggleFocusMode={() => {}}
              vessels={[]}
            />

            <div className="flex-1 overflow-y-auto p-6">
              <h1 className="text-2xl font-bold mb-4">Statistics & Reports</h1>
              <div className="text-gray-500">
                <p>This page is currently in progress...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportPage;
