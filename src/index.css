@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #3b82f6;
  --secondary: #14b8a6;
  --accent: #f97316;
  --success: #22c55e;
  --warning: #eab308;
  --error: #ef4444;
}

html, body {
  height: 100%;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}

/* Dark mode styles */
.dark {
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --bg-tertiary: #252525;
  --text-primary: #ffffff;
  --text-secondary: #a0a0a0;
  color-scheme: dark;
}

/* Light mode styles */
.light {
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f5;
  --bg-tertiary: #e5e5e5;
  --text-primary: #121212;
  --text-secondary: #5c5c5c;
  color-scheme: light;
}

/* Light mode visual polish (override some dark-tailwind utilities used in components) */
.light .bg-gray-800 { background-color: #f8fafc !important; }
.light .bg-gray-900 { background-color: #ffffff !important; }
.light .border-gray-700 { border-color: #e5e7eb !important; }
.light .text-gray-300 { color: #374151 !important; }
.light .text-gray-400 { color: #6b7280 !important; }
.light .text-gray-500 { color: #6b7280 !important; }
.light .text-gray-600 { color: #6b7280 !important; }
.light .bg-black\/60 { background-color: rgba(0,0,0,0.5) !important; }

/* Light mode vessel panel specific styles */
.light .bg-white\/5 { background-color: #f9fafb !important; }
.light .border-white\/10 { border-color: #e5e7eb !important; }
.light .border-white\/20 { border-color: #d1d5db !important; }
.light .shadow-black\/40 { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1) !important; }

/* Light mode map control styles */
.light .maplibregl-ctrl-group > button {
  background-color: #ffffff !important;
  border-color: #e5e7eb !important;
  color: #111827 !important;
}
.light .maplibregl-ctrl-group > button:hover { background-color: #f3f4f6 !important; }
.light .maplibregl-ctrl-attrib { background-color: rgba(255,255,255,0.85) !important; color: #374151 !important; }
.light .maplibregl-ctrl-attrib a { color: #2563eb !important; }

/* Smooth transitions */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #d97706;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

/* MapLibre popup theme to ensure readable text */
.maplibregl-popup-content {
  color: #111827; /* gray-900 */
  font-size: 12px;
}
.maplibregl-popup-content h4 {
  margin: 0 0 4px 0;
  font-weight: 600;
  font-size: 14px;
}
.maplibregl-popup-tip,
.maplibregl-popup-content {
  background: #ffffff;
}
.maplibregl-popup-close-button {
  color: #4b5563; /* gray-600 */
}

/* Custom vessel hover popup styles */
.vessel-hover-popup .maplibregl-popup-content {
  background: transparent !important;
  padding: 0 !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  border: none !important;
}
.vessel-hover-popup .maplibregl-popup-tip {
  border-top-color: transparent !important;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
  border-right-color: transparent !important;
}

/* maplibre-theme plugin */
.maplibregl-map {
  --ml-ctrl-border-radius: 1rem;
  --ml-font: 16px/24px system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  --ml-font-attribution: inherit;
  --ml-c-bg-1: #ffffff;
  --ml-c-bg-2: #f0f0f0;
  --ml-c-bg-3: #d7d7d7;
  --ml-c-icon-1: #333333;
  --ml-c-icon-2: #000000;
  --ml-c-active: #1da1f2;
  --ml-c-error: #e54e33;
  --ml-c-outline: #dba726;
  --ml-o-disabled: 0.25;
  --ml-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.15), 0 1px 2px -1px rgb(0 0 0 / 0.15);
  --ml-shadow-active: 0 10px 15px -3px rgb(0 0 0 / 0.15), 0 4px 6px -2px rgb(0 0 0 / 0.15);
  --ml-c-link-1: #333333;
  --ml-c-link-2: #000000;
  --ml-c-logo-1: #ffffff;
  --ml-c-logo-2: #d7d7d7;
  --ml-c-geoloc: #1da1f2;
  --ml-font-icons: maplibregl-icons-lucide;
}

.dark .maplibregl-map {
  --ml-c-bg-1: #111725;
  --ml-c-bg-2: #414853;
  --ml-c-bg-3: #32363f;
  --ml-c-icon-1: #cbd5e1;
  --ml-c-icon-2: #ffffff;
  --ml-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.15), 0 1px 2px -1px rgb(0 0 0 / 0.15);
  --ml-c-link-1: #cbd5e1;
  --ml-c-link-2: #ffffff;
  --ml-c-logo-1: #111725;
  --ml-c-logo-2: #32363f;
}
