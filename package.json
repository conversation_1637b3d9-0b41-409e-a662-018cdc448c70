{"name": "MarineWeb", "private": true, "version": "1.8.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "docker-run": "docker run -d --name marine-fe-uat-web -p 8080:80 registry.phucnguyenmarine.com/marine-fe-uat-web:latest", "docker-build": "docker build --platform linux/amd64 -t registry.phucnguyenmarine.com/marine-fe-uat-web:latest .", "docker-publish": "docker push registry.phucnguyenmarine.com/marine-fe-uat-web:latest"}, "dependencies": {"@turf/turf": "^7.2.0", "country-flag-icons": "^1.5.21", "flag-icons": "^7.5.0", "i18next": "^25.5.2", "lucide-react": "^0.344.0", "maplibre-gl": "^5.7.2", "maplibre-gl-basemaps": "^0.1.3", "maplibre-theme": "^1.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^16.0.0", "socket.io-client": "^4.8.1", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/i18next": "^12.1.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}