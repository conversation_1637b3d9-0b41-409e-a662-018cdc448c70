# Build stage: compile the Vite app into static assets
FROM node:20-alpine AS builder
WORKDIR /app

# Install dependencies using the lockfile for reproducible builds
COPY package.json package-lock.json ./
RUN npm ci

# Copy source and build production bundle
COPY . .
RUN npm run build

# Runtime stage: serve the built assets via Nginx
FROM nginx:1.27-alpine AS runner

# Copy custom Nginx config to enable SPA routing
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy compiled assets from the builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
