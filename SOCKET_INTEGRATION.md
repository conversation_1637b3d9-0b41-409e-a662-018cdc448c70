# Socket.IO AIS Vessel Tracking Integration

This document describes the integration of Socket.IO real-time AIS vessel tracking into the React MapView component.

## Features

- **Real-time vessel tracking** via Socket.IO connection to `ws://103.124.92.184:7979/public`
- **Interactive vessel markers** with rotation based on course over ground (COG)
- **Vessel information panel** showing connection status and vessel details
- **Automatic vessel updates** with position, speed, and course information
- **Popup information** when clicking on vessel markers

## Components

### `useVesselTracking` Hook
- Manages Socket.IO connection lifecycle
- Handles vessel data parsing and marker management
- Provides vessel list and connection status

### `VesselInfoPanel` Component
- Displays connection status with visual indicator
- Shows list of tracked vessels with key information
- Real-time updates as vessels are added/updated

### `MapView` Component
- Integrates vessel tracking with MapLibre GL map
- Uses default vessel icon from `/assets/vessels/default.png`
- Handles map initialization and vessel marker rendering

## Data Flow

1. Socket.IO connects to AIS data stream
2. Incoming AIS messages are parsed for vessel data
3. Vessel markers are created/updated on the map with rotation
4. Vessel information panel updates in real-time
5. Popup information shows detailed vessel data on click

## Vessel Data Structure

```typescript
interface VesselInfo {
  mmsi: string;           // Maritime Mobile Service Identity
  name: string;           // Vessel name
  lat: number;            // Latitude
  lon: number;            // Longitude
  cog: number;            // Course over ground (degrees)
  speed: number;          // Speed in knots
  timestamp: string;      // Last update time
  aisType?: number;       // AIS message type
}
```

## Socket Events

- `connect` - Connection established
- `disconnect` - Connection lost
- `RES_MSG` - Main AIS data stream
- `VESSEL_UPDATE` - Legacy vessel update messages
- `SUB_AIS` - Subscription confirmation
- `SUBSCRIBED` - Successfully subscribed to AIS data
- `UNSUBSCRIBED` - Unsubscribed from AIS data

## Usage

The integration is automatically active when the MapView component is rendered. No additional configuration is required - the component will:

1. Connect to the AIS data stream
2. Display vessel markers on the map
3. Show connection status and vessel information
4. Handle real-time updates automatically

## Dependencies

- `socket.io-client` - WebSocket client for real-time communication
- `maplibre-gl` - Map rendering and marker management
- React hooks for state management and lifecycle handling
