# MarineWeb - Navigation Aid Monitoring System

A real-time marine monitoring application for tracking vessels and Aids to Navigation (AtoNs) with interactive map visualization and comprehensive management features.

## 🌊 Overview

MarineWeb is a comprehensive marine monitoring solution that provides real-time tracking of vessels and navigation aids. Built with modern web technologies, it offers an intuitive interface for maritime professionals to monitor vessel movements, manage navigation equipment, and ensure maritime safety.

## ✨ Key Features

### 🚢 Vessel Monitoring
- **Real-time Tracking**: Live vessel positions via AIS data and WebSocket connections
- **Historical Playback**: Review vessel routes and movements over time
- **Advanced Filtering**: Filter by vessel type, navigation status, and custom criteria
- **Detailed Information**: Comprehensive vessel data including speed, course, and technical specifications

### 🗺️ Interactive Mapping
- **MapLibre GL**: High-performance vector map rendering
- **Multiple Basemaps**: Switch between different map styles and providers
- **Layer Control**: Toggle vessel, AtoN, and area layers independently
- **Real-time Updates**: Live position updates with smooth animations

### ⚓ Navigation Aids (AtoNs)
- **Equipment Monitoring**: Track lighthouses, beacons, buoys, and other navigation aids
- **Status Management**: Monitor operational status and maintenance schedules
- **Type Classification**: Comprehensive categorization of navigation equipment
- **Location Mapping**: Precise positioning of all navigation aids

### 📊 Management Dashboard
- **Multi-panel Interface**: Organized information display with collapsible panels
- **Alert System**: Real-time notifications for equipment issues and maintenance
- **User Management**: Role-based access control and user administration
- **Reporting**: Generate reports on vessel traffic and equipment status

### 🌐 Internationalization
- **Multi-language Support**: Vietnamese and English interfaces
- **Localized Content**: Region-specific formatting and terminology
- **Dynamic Switching**: Change language without page reload

## 🛠️ Technology Stack

### Frontend
- **React 18.3.1** - Modern UI framework with hooks and concurrent features
- **TypeScript 5.5.3** - Type-safe development with enhanced IDE support
- **Vite 5.4.2** - Fast build tool and development server
- **Tailwind CSS 3.4.1** - Utility-first CSS framework for rapid styling

### Mapping & Visualization
- **MapLibre GL 5.7.2** - Open-source vector map rendering
- **Turf.js 7.2.0** - Geospatial analysis and calculations
- **Lucide React** - Modern icon library for consistent UI

### State Management & Data
- **Zustand 5.0.8** - Lightweight state management
- **Socket.IO Client 4.8.1** - Real-time bidirectional communication
- **React i18next 25.5.2** - Internationalization framework

## 🏗️ Architecture

### Domain-Driven Structure
```
src/
├── app/                    # Application configuration
├── components/             # Reusable UI components
│   ├── common/            # Shared components (Header, Sidebar)
│   ├── forms/             # Form components and inputs
│   ├── panels/            # Information display panels
│   ├── map/               # Map-related components
│   └── ui/                # Basic UI elements
├── domains/               # Business logic by domain
│   ├── vessels/           # Vessel-related functionality
│   ├── atons/             # Navigation aids management
│   ├── areas/             # Geographic areas
│   └── auth/              # Authentication & authorization
├── shared/                # Shared utilities and services
│   ├── api/               # API client and communication
│   ├── hooks/             # Reusable React hooks
│   ├── lib/               # Core libraries (LayerManager, contexts)
│   ├── types/             # TypeScript type definitions
│   └── utils/             # Utility functions
└── pages/                 # Page components and routing
```

### Key Design Principles
- **Domain Separation**: Business logic organized by functional domains
- **Component Reusability**: Modular components for consistent UI
- **Type Safety**: Comprehensive TypeScript coverage
- **Performance**: Optimized rendering and data handling
- **Maintainability**: Clean code structure with clear dependencies

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn package manager

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd navaid-web-sample

# Install dependencies
npm install

# Start development server
npm run dev
```

### Environment Configuration
Create a `.env` file with required environment variables:
```env
VITE_API_BASE_URL=your_api_endpoint
VITE_SOCKET_URL=your_websocket_endpoint
```

### Development
- **Development Server**: `npm run dev` - Starts Vite dev server with hot reload
- **Build**: `npm run build` - Creates production build
- **Preview**: `npm run preview` - Preview production build locally
- **Lint**: `npm run lint` - Run ESLint for code quality

## 🐳 Docker Deployment

### Build & Run
```bash
# Build Docker image
npm run docker-build

# Run container
npm run docker-run

# Publish to registry
npm run docker-publish
```

## 📱 Features in Detail

### Real-time Vessel Tracking
- Live AIS data integration with WebSocket connections
- Automatic position updates with configurable intervals
- Vessel trail visualization showing historical movement
- Speed and course indicators with directional arrows

### Advanced Filtering System
- Multi-criteria filtering by vessel type, size, and status
- Navigation status filtering (underway, anchored, moored, etc.)
- Geographic area-based filtering
- Custom search functionality for specific vessels

### Interactive Map Controls
- Zoom and pan controls with smooth animations
- Layer visibility toggles for different data types
- Basemap switching between satellite, street, and nautical charts
- Full-screen mode for detailed analysis

### Equipment Management
- Comprehensive AtoN database with detailed specifications
- Maintenance scheduling and alert system
- Status monitoring with real-time updates
- Equipment categorization and search capabilities

## 🔧 Configuration

### Path Aliases
The project uses TypeScript path aliases for clean imports:
- `@/*` - src directory root
- `@/shared/*` - Shared utilities and services
- `@/domains/*` - Domain-specific business logic
- `@/components/*` - UI components

### Map Configuration
- Default center: Configurable via constants
- Zoom levels: 1-20 with appropriate layer visibility
- Basemap providers: Multiple options with fallbacks
- Performance optimization for large datasets

## 🤝 Contributing

1. Follow the established domain-driven architecture
2. Use TypeScript for all new code
3. Maintain component reusability principles
4. Add appropriate tests for new features
5. Follow the existing code style and conventions

## 📄 License

This project is proprietary software developed for marine monitoring applications.
