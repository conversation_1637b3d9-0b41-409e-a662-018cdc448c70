module.exports = {
  // locales paths
  localesPaths: ['src/i18n/locales'],
  
  // source language
  sourceLanguage: 'vi',
  
  // display language
  displayLanguage: 'en',
  
  // namespace
  namespace: true,
  defaultNamespace: 'common',
  namespaces: ['common', 'navigation', 'dashboard', 'device', 'alerts', 'map', 'auth', 'timeline'],
  
  // key style
  keystyle: 'nested',
  
  // sort keys
  sortKeys: true,
  
  // enabled frameworks
  enabledFrameworks: ['react', 'i18next'],
  
  // path matcher
  pathMatcher: '{locale}/{namespace}.json',
  
  // directory structure
  dirStructure: 'file',
  
  // usage scanning ignore
  usage: {
    scanningIgnore: [
      'node_modules/**',
      'dist/**',
      'build/**'
    ]
  }
};
