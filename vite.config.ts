import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/shared': resolve(__dirname, 'src/shared'),
      '@/domains': resolve(__dirname, 'src/domains'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/config': resolve(__dirname, 'src/config'),
      '@/pages': resolve(__dirname, 'src/pages'),
      '@/app': resolve(__dirname, 'src/app'),
    },
  },
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version ?? '0.0.0'),
  },
  // optimizeDeps: {
  //   exclude: ['lucide-react'],
  // },
});
